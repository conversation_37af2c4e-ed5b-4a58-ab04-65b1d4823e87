-- Script de création de la base de données ZinStore
-- Système de Gestion de Supermarché

-- <PERSON>réer la base de données
CREATE DATABASE IF NOT EXISTS zinstore_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE zinstore_db;

-- Table des utilisateurs
CREATE TABLE Utilisateurs (
    Id INT AUTO_INCREMENT PRIMARY KEY,
    NomUtilisateur VARCHAR(50) NOT NULL UNIQUE,
    MotDePasse VARCHAR(100) NOT NULL,
    Nom VARCHAR(100) NOT NULL,
    Prenom VARCHAR(100) NOT NULL,
    Email VARCHAR(150) NOT NULL UNIQUE,
    Telephone VARCHAR(20),
    TypeUtilisateur INT NOT NULL, -- 1=Admin, 2=Gerant, 3=Vendeur, 4=Caissier, 5=Magasinier
    DerniereConnexion DATETIME,
    EstConnecte BOOLEAN DEFAULT FALSE,
    Adresse VARCHAR(500),
    Salaire DECIMAL(10,2),
    DateEmbauche DATE,
    Notes VARCHAR(200),
    DateCreation DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    DateModification DATETIME,
    UtilisateurCreation VARCHAR(50) NOT NULL,
    UtilisateurModification VARCHAR(50),
    EstActif BOOLEAN DEFAULT TRUE,
    EstSupprime BOOLEAN DEFAULT FALSE
);

-- Table des catégories
CREATE TABLE Categories (
    Id INT AUTO_INCREMENT PRIMARY KEY,
    Nom VARCHAR(100) NOT NULL,
    Description VARCHAR(500),
    Code VARCHAR(10),
    CategorieParentId INT,
    DateCreation DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    DateModification DATETIME,
    UtilisateurCreation VARCHAR(50) NOT NULL,
    UtilisateurModification VARCHAR(50),
    EstActif BOOLEAN DEFAULT TRUE,
    EstSupprime BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (CategorieParentId) REFERENCES Categories(Id)
);

-- Table des produits
CREATE TABLE Produits (
    Id INT AUTO_INCREMENT PRIMARY KEY,
    Nom VARCHAR(100) NOT NULL,
    Description VARCHAR(500),
    CodeBarre VARCHAR(50) NOT NULL UNIQUE,
    CodeInterne VARCHAR(20),
    CategorieId INT NOT NULL,
    PrixAchat DECIMAL(10,2) NOT NULL,
    PrixVente DECIMAL(10,2) NOT NULL,
    PrixPromotion DECIMAL(10,2),
    DateDebutPromotion DATETIME,
    DateFinPromotion DATETIME,
    Unite VARCHAR(20) NOT NULL DEFAULT 'Pièce',
    Poids DECIMAL(8,3),
    Volume DECIMAL(8,3),
    Marque VARCHAR(100),
    DateExpiration DATE,
    StockMinimum INT DEFAULT 0,
    StockMaximum INT DEFAULT 1000,
    TVA DECIMAL(5,2) DEFAULT 19.00,
    ImagePath VARCHAR(200),
    Notes VARCHAR(500),
    DateCreation DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    DateModification DATETIME,
    UtilisateurCreation VARCHAR(50) NOT NULL,
    UtilisateurModification VARCHAR(50),
    EstActif BOOLEAN DEFAULT TRUE,
    EstSupprime BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (CategorieId) REFERENCES Categories(Id)
);

-- Table des clients
CREATE TABLE Clients (
    Id INT AUTO_INCREMENT PRIMARY KEY,
    Nom VARCHAR(100),
    Prenom VARCHAR(100),
    RaisonSociale VARCHAR(100),
    NumeroRegistreCommerce VARCHAR(20),
    NumeroIdentificationFiscale VARCHAR(20),
    Email VARCHAR(150) NOT NULL UNIQUE,
    Telephone VARCHAR(20),
    TelephoneSecondaire VARCHAR(20),
    Adresse VARCHAR(500),
    Ville VARCHAR(100),
    CodePostal VARCHAR(10),
    Pays VARCHAR(100) DEFAULT 'Algérie',
    DateNaissance DATE,
    EstEntreprise BOOLEAN DEFAULT FALSE,
    LimiteCredit DECIMAL(10,2) DEFAULT 0,
    SoldeCompte DECIMAL(10,2) DEFAULT 0,
    PointsFidelite INT DEFAULT 0,
    Notes VARCHAR(500),
    DateCreation DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    DateModification DATETIME,
    UtilisateurCreation VARCHAR(50) NOT NULL,
    UtilisateurModification VARCHAR(50),
    EstActif BOOLEAN DEFAULT TRUE,
    EstSupprime BOOLEAN DEFAULT FALSE
);

-- Table des fournisseurs
CREATE TABLE Fournisseurs (
    Id INT AUTO_INCREMENT PRIMARY KEY,
    RaisonSociale VARCHAR(150) NOT NULL,
    NomContact VARCHAR(100),
    PrenomContact VARCHAR(100),
    NumeroRegistreCommerce VARCHAR(20) NOT NULL,
    NumeroIdentificationFiscale VARCHAR(20),
    Email VARCHAR(150) NOT NULL,
    Telephone VARCHAR(20),
    Fax VARCHAR(20),
    Adresse VARCHAR(500),
    Ville VARCHAR(100),
    CodePostal VARCHAR(10),
    Pays VARCHAR(100) DEFAULT 'Algérie',
    SiteWeb VARCHAR(100),
    DelaiPaiement INT DEFAULT 30,
    LimiteCredit DECIMAL(10,2) DEFAULT 0,
    SoldeCompte DECIMAL(10,2) DEFAULT 0,
    ConditionsCommerciales VARCHAR(500),
    Notes VARCHAR(500),
    DateCreation DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    DateModification DATETIME,
    UtilisateurCreation VARCHAR(50) NOT NULL,
    UtilisateurModification VARCHAR(50),
    EstActif BOOLEAN DEFAULT TRUE,
    EstSupprime BOOLEAN DEFAULT FALSE
);

-- Table des stocks
CREATE TABLE Stocks (
    Id INT AUTO_INCREMENT PRIMARY KEY,
    ProduitId INT NOT NULL,
    QuantiteDisponible DECIMAL(10,2) NOT NULL DEFAULT 0,
    QuantiteReservee DECIMAL(10,2) DEFAULT 0,
    QuantiteMinimum DECIMAL(10,2) DEFAULT 0,
    QuantiteMaximum DECIMAL(10,2) DEFAULT 1000,
    Emplacement VARCHAR(100),
    DateDernierInventaire DATETIME,
    CoutMoyenPondere DECIMAL(10,2),
    ValeurStock DECIMAL(12,2) DEFAULT 0,
    Notes VARCHAR(200),
    DateCreation DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    DateModification DATETIME,
    UtilisateurCreation VARCHAR(50) NOT NULL,
    UtilisateurModification VARCHAR(50),
    EstActif BOOLEAN DEFAULT TRUE,
    EstSupprime BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (ProduitId) REFERENCES Produits(Id)
);

-- Table des ventes
CREATE TABLE Ventes (
    Id INT AUTO_INCREMENT PRIMARY KEY,
    NumeroFacture VARCHAR(20) NOT NULL UNIQUE,
    DateVente DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    ClientId INT,
    UtilisateurId INT NOT NULL,
    SousTotal DECIMAL(10,2) NOT NULL,
    Remise DECIMAL(10,2) DEFAULT 0,
    MontantTVA DECIMAL(10,2) DEFAULT 0,
    Total DECIMAL(10,2) NOT NULL,
    MontantPaye DECIMAL(10,2) DEFAULT 0,
    MontantRendu DECIMAL(10,2) DEFAULT 0,
    ModePaiement INT NOT NULL, -- 1=Especes, 2=Carte, 3=Cheque, 4=Virement, 5=Credit
    Statut INT NOT NULL DEFAULT 1, -- 1=EnCours, 2=Validee, 3=Annulee, 4=Retournee
    Notes VARCHAR(500),
    EstFacturee BOOLEAN DEFAULT FALSE,
    DateFacturation DATETIME,
    DateCreation DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    DateModification DATETIME,
    UtilisateurCreation VARCHAR(50) NOT NULL,
    UtilisateurModification VARCHAR(50),
    EstActif BOOLEAN DEFAULT TRUE,
    EstSupprime BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (ClientId) REFERENCES Clients(Id),
    FOREIGN KEY (UtilisateurId) REFERENCES Utilisateurs(Id)
);

-- Table des détails de vente
CREATE TABLE VenteDetails (
    Id INT AUTO_INCREMENT PRIMARY KEY,
    VenteId INT NOT NULL,
    ProduitId INT NOT NULL,
    Quantite DECIMAL(10,2) NOT NULL,
    PrixUnitaire DECIMAL(10,2) NOT NULL,
    Remise DECIMAL(10,2) DEFAULT 0,
    TauxTVA DECIMAL(5,2) DEFAULT 0,
    MontantTVA DECIMAL(10,2) DEFAULT 0,
    SousTotal DECIMAL(10,2) NOT NULL,
    Notes VARCHAR(200),
    DateCreation DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    DateModification DATETIME,
    UtilisateurCreation VARCHAR(50) NOT NULL,
    UtilisateurModification VARCHAR(50),
    EstActif BOOLEAN DEFAULT TRUE,
    EstSupprime BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (VenteId) REFERENCES Ventes(Id),
    FOREIGN KEY (ProduitId) REFERENCES Produits(Id)
);

-- Table des achats
CREATE TABLE Achats (
    Id INT AUTO_INCREMENT PRIMARY KEY,
    NumeroCommande VARCHAR(20) NOT NULL UNIQUE,
    NumeroFactureFournisseur VARCHAR(20),
    DateCommande DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    DateLivraison DATETIME,
    DateFacture DATETIME,
    FournisseurId INT NOT NULL,
    UtilisateurId INT NOT NULL,
    SousTotal DECIMAL(10,2) NOT NULL,
    Remise DECIMAL(10,2) DEFAULT 0,
    MontantTVA DECIMAL(10,2) DEFAULT 0,
    Total DECIMAL(10,2) NOT NULL,
    MontantPaye DECIMAL(10,2) DEFAULT 0,
    Statut INT NOT NULL DEFAULT 1, -- 1=Commande, 2=Recu, 3=Facture, 4=Paye, 5=Annule
    Notes VARCHAR(500),
    DateCreation DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    DateModification DATETIME,
    UtilisateurCreation VARCHAR(50) NOT NULL,
    UtilisateurModification VARCHAR(50),
    EstActif BOOLEAN DEFAULT TRUE,
    EstSupprime BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (FournisseurId) REFERENCES Fournisseurs(Id),
    FOREIGN KEY (UtilisateurId) REFERENCES Utilisateurs(Id)
);

-- Table des détails d'achat
CREATE TABLE AchatDetails (
    Id INT AUTO_INCREMENT PRIMARY KEY,
    AchatId INT NOT NULL,
    ProduitId INT NOT NULL,
    QuantiteCommandee DECIMAL(10,2) NOT NULL,
    QuantiteRecue DECIMAL(10,2) DEFAULT 0,
    PrixUnitaire DECIMAL(10,2) NOT NULL,
    Remise DECIMAL(10,2) DEFAULT 0,
    TauxTVA DECIMAL(5,2) DEFAULT 0,
    MontantTVA DECIMAL(10,2) DEFAULT 0,
    SousTotal DECIMAL(10,2) NOT NULL,
    Notes VARCHAR(200),
    DateCreation DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    DateModification DATETIME,
    UtilisateurCreation VARCHAR(50) NOT NULL,
    UtilisateurModification VARCHAR(50),
    EstActif BOOLEAN DEFAULT TRUE,
    EstSupprime BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (AchatId) REFERENCES Achats(Id),
    FOREIGN KEY (ProduitId) REFERENCES Produits(Id)
);

-- Table des mouvements de stock
CREATE TABLE MouvementStocks (
    Id INT AUTO_INCREMENT PRIMARY KEY,
    ProduitId INT NOT NULL,
    TypeMouvement INT NOT NULL, -- 1=Entree, 2=Sortie, 3=Ajustement, 4=Inventaire, 5=Retour
    Quantite DECIMAL(10,2) NOT NULL,
    QuantiteAvant DECIMAL(10,2) DEFAULT 0,
    QuantiteApres DECIMAL(10,2) DEFAULT 0,
    DateMouvement DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    Reference VARCHAR(200),
    Motif VARCHAR(500),
    UtilisateurId INT,
    CoutUnitaire DECIMAL(10,2),
    ValeurMouvement DECIMAL(12,2),
    Notes VARCHAR(500),
    DateCreation DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    DateModification DATETIME,
    UtilisateurCreation VARCHAR(50),
    UtilisateurModification VARCHAR(50),
    EstActif BOOLEAN DEFAULT TRUE,
    EstSupprime BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (ProduitId) REFERENCES Produits(Id),
    FOREIGN KEY (UtilisateurId) REFERENCES Utilisateurs(Id)
);

-- Insérer un utilisateur administrateur par défaut
INSERT INTO Utilisateurs (
    NomUtilisateur, MotDePasse, Nom, Prenom, Email, TypeUtilisateur, 
    UtilisateurCreation, DateCreation, EstActif
) VALUES (
    'admin', 'YWRtaW4xMjNaaW5TdG9yZVNhbHQ=', 'Administrateur', 'Système', '<EMAIL>', 1,
    'SYSTEM', NOW(), TRUE
);

-- Insérer quelques catégories par défaut
INSERT INTO Categories (Nom, Description, Code, UtilisateurCreation) VALUES
('Alimentation', 'Produits alimentaires', 'ALI', 'admin'),
('Boissons', 'Boissons diverses', 'BOI', 'admin'),
('Hygiène', 'Produits d\'hygiène', 'HYG', 'admin'),
('Électronique', 'Appareils électroniques', 'ELE', 'admin');

-- Insérer quelques produits d'exemple
INSERT INTO Produits (
    Nom, Description, CodeBarre, CategorieId, PrixAchat, PrixVente, Unite, 
    UtilisateurCreation
) VALUES
('Pain de mie', 'Pain de mie complet', '1234567890123', 1, 80.00, 120.00, 'Pièce', 'admin'),
('Eau minérale 1.5L', 'Eau minérale naturelle', '1234567890124', 2, 25.00, 40.00, 'Bouteille', 'admin'),
('Savon liquide', 'Savon liquide pour les mains', '1234567890125', 3, 150.00, 220.00, 'Flacon', 'admin');

-- Créer les index pour optimiser les performances
CREATE INDEX idx_produits_codebarre ON Produits(CodeBarre);
CREATE INDEX idx_ventes_date ON Ventes(DateVente);
CREATE INDEX idx_ventes_client ON Ventes(ClientId);
CREATE INDEX idx_stocks_produit ON Stocks(ProduitId);
CREATE INDEX idx_mouvements_produit ON MouvementStocks(ProduitId);
CREATE INDEX idx_mouvements_date ON MouvementStocks(DateMouvement);

-- Créer des vues utiles
CREATE VIEW vue_stock_actuel AS
SELECT 
    p.Id as ProduitId,
    p.Nom as NomProduit,
    p.CodeBarre,
    c.Nom as Categorie,
    s.QuantiteDisponible,
    s.QuantiteReservee,
    s.QuantiteMinimum,
    p.PrixVente,
    s.ValeurStock,
    CASE 
        WHEN s.QuantiteDisponible <= s.QuantiteMinimum THEN 'Rupture'
        WHEN s.QuantiteDisponible <= s.QuantiteMinimum * 1.5 THEN 'Faible'
        ELSE 'Normal'
    END as StatutStock
FROM Produits p
LEFT JOIN Stocks s ON p.Id = s.ProduitId
LEFT JOIN Categories c ON p.CategorieId = c.Id
WHERE p.EstSupprime = FALSE;

COMMIT;

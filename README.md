# ZinStore - Système de Gestion de Supermarché

## 📋 Description

ZinStore est un système complet de gestion de supermarché développé en C# avec WinForms et Krypton Toolkit, conçu spécialement pour les entreprises algériennes. Le système offre une interface moderne en français et couvre tous les aspects de la gestion commerciale et comptable.

## 🏗️ Architecture du Projet

Le projet suit une architecture en couches (Layered Architecture) avec 5 projets principaux :

### 1. **Super_Market.Core** - Couche des Modèles
- **Modèles de données** : Toutes les entités métier (Produit, Client, Vente, etc.)
- **Enums** : Types énumérés (TypeUtilisateur, StatutVente, ModePaiement, etc.)
- **BaseEntity** : Classe de base avec propriétés communes (Id, DateCreation, etc.)

### 2. **Super_Market.Data** - Couche d'Accès aux Données (DAL)
- **Repositories** : Accès aux données avec Dapper ORM
- **Interfaces** : Contrats pour les repositories
- **DatabaseConfig** : Configuration de connexion MySQL

### 3. **Super_Market.Business** - Couche Logique Métier (BLL)
- **Services** : Logique métier et validation
- **Règles de gestion** : Validation des données et règles business

### 4. **Super_Market.App** - Application Principale de Gestion
- **Interface complète** : Gestion de tous les modules
- **Formulaires** : Dashboard, Produits, Clients, Stock, Rapports, etc.
- **Utilisateurs** : Administrateurs, Gérants, Employés

### 5. **Super_Market.POS** - Application Point de Vente
- **Interface POS** : Optimisée pour la vente rapide
- **Écran tactile** : Interface adaptée aux écrans tactiles
- **Utilisateurs** : Caissiers, Vendeurs

## 🚀 Fonctionnalités

### 🧾 Modules Principaux
- **Gestion des Ventes** - Facturation, point de vente, gestion des clients
- **Gestion des Achats** - Commandes fournisseurs, réception de marchandises
- **Gestion du Stock** - Inventaire, mouvements, alertes de rupture
- **Gestion des Clients** - Fichier client, comptes, historique
- **Gestion des Fournisseurs** - Fichier fournisseur, conditions commerciales
- **Gestion des Produits** - Catalogue, catégories, prix
- **Comptabilité Générale** - Écritures, comptes, bilan, compte de résultat
- **Rapports et Statistiques** - Tableaux de bord, analyses

### 🔐 Sécurité et Utilisateurs
- Système d'authentification sécurisé
- Gestion des rôles et permissions
- Traçabilité des opérations
- Sauvegarde automatique

### 🎨 Interface Utilisateur
- Interface moderne avec Krypton Toolkit
- Entièrement en français
- Responsive et intuitive
- Thèmes personnalisables

## 🛠️ Technologies Utilisées

- **Framework** : .NET Framework 4.6.2
- **Interface** : WinForms avec Krypton Toolkit
- **Base de données** : MySQL
- **ORM** : Dapper
- **Langage** : C#

## 📦 Installation

### Prérequis
- Visual Studio 2019 ou plus récent
- .NET Framework 4.6.2
- MySQL Server 5.7 ou plus récent
- MySQL Workbench (recommandé)

### Étapes d'installation

1. **Cloner le projet**
   ```bash
   git clone [URL_DU_PROJET]
   cd Super_Market
   ```

2. **Configurer la base de données**
   - Installer MySQL Server
   - Exécuter le script `Database/zinstore_database.sql`
   - Modifier les chaînes de connexion dans les fichiers App.config

3. **Restaurer les packages NuGet**
   ```bash
   nuget restore Super_Market.sln
   ```

4. **Compiler la solution**
   - Ouvrir `Super_Market.sln` dans Visual Studio
   - Compiler la solution (Build → Build Solution)

5. **Exécuter les applications**
   - **App.exe** : Application principale de gestion
   - **POS.exe** : Application point de vente

## 🔧 Configuration

### Base de données
Modifier les chaînes de connexion dans :
- `Super_Market.App/App.config`
- `Super_Market.POS/App.config`

```xml
<connectionStrings>
    <add name="DefaultConnection" 
         connectionString="Server=localhost;Database=zinstore_db;Uid=root;Pwd=******************;" 
         providerName="MySql.Data.MySqlClient" />
</connectionStrings>
```

### Utilisateur par défaut
- **Nom d'utilisateur** : admin
- **Mot de passe** : admin123

## 📱 Applications

### Super_Market.App (Gestion)
Application principale pour la gestion complète du supermarché :
- Dashboard avec statistiques
- Gestion des produits et catégories
- Gestion des clients et fournisseurs
- Gestion du stock et inventaire
- Rapports et analyses
- Configuration et paramètres

### Super_Market.POS (Point de Vente)
Application optimisée pour les caisses :
- Interface tactile simplifiée
- Lecture de codes-barres
- Gestion du panier
- Modes de paiement multiples
- Impression de tickets

## 🗂️ Structure des Dossiers

```
Super_Market/
├── Super_Market.Core/          # Modèles et entités
│   ├── Models/                 # Classes métier
│   └── Enums/                  # Types énumérés
├── Super_Market.Data/          # Accès aux données
│   ├── Repositories/           # Repositories Dapper
│   └── Interfaces/             # Contrats
├── Super_Market.Business/      # Logique métier
│   └── Services/               # Services business
├── Super_Market.App/           # Application gestion
│   └── Forms/                  # Formulaires WinForms
├── Super_Market.POS/           # Application POS
├── Database/                   # Scripts SQL
└── packages/                   # Packages NuGet
```

## 🔑 Comptes Utilisateurs

Le système supporte plusieurs types d'utilisateurs :

1. **Administrateur** : Accès complet à toutes les fonctionnalités
2. **Gérant** : Gestion opérationnelle, pas d'accès aux utilisateurs
3. **Vendeur** : Ventes, clients, produits (lecture)
4. **Caissier** : Point de vente uniquement
5. **Magasinier** : Gestion du stock et des achats

## 📊 Base de Données

La base de données MySQL contient les tables principales :
- Utilisateurs, Clients, Fournisseurs
- Produits, Categories, Stocks
- Ventes, VenteDetails, Achats, AchatDetails
- MouvementStocks, CompteGeneral
- Revenus, Depenses, Parametres

## 🤝 Contribution

Pour contribuer au projet :
1. Fork le projet
2. Créer une branche feature (`git checkout -b feature/AmazingFeature`)
3. Commit les changements (`git commit -m 'Add some AmazingFeature'`)
4. Push vers la branche (`git push origin feature/AmazingFeature`)
5. Ouvrir une Pull Request

## 📄 Licence

Ce projet est sous licence MIT. Voir le fichier `LICENSE` pour plus de détails.

## 📞 Support

Pour toute question ou support :
- Email : <EMAIL>
- Documentation : [Wiki du projet]
- Issues : [GitHub Issues]

---

**ZinStore** - Développé avec ❤️ pour les entreprises algériennes

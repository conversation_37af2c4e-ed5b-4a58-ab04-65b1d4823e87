namespace Super_Market.App.Forms
{
    partial class FRM_CATEGORIES
    {
        private System.ComponentModel.IContainer components = null;

        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        private void InitializeComponent()
        {
            this.panelMain = new Krypton.Toolkit.KryptonPanel();
            this.splitContainer = new System.Windows.Forms.SplitContainer();
            this.panelGrille = new Krypton.Toolkit.KryptonPanel();
            this.dgvCategories = new Krypton.Toolkit.KryptonDataGridView();
            this.panelRecherche = new Krypton.Toolkit.KryptonPanel();
            this.lblRecherche = new Krypton.Toolkit.KryptonLabel();
            this.txtRecherche = new Krypton.Toolkit.KryptonTextBox();
            this.panelDetails = new Krypton.Toolkit.KryptonPanel();
            this.groupBoxDetails = new Krypton.Toolkit.KryptonGroupBox();
            this.lblCode = new Krypton.Toolkit.KryptonLabel();
            this.txtCode = new Krypton.Toolkit.KryptonTextBox();
            this.lblNom = new Krypton.Toolkit.KryptonLabel();
            this.txtNom = new Krypton.Toolkit.KryptonTextBox();
            this.lblDescription = new Krypton.Toolkit.KryptonLabel();
            this.txtDescription = new Krypton.Toolkit.KryptonTextBox();
            this.lblCategorieParent = new Krypton.Toolkit.KryptonLabel();
            this.cmbCategorieParent = new Krypton.Toolkit.KryptonComboBox();
            this.chkActif = new Krypton.Toolkit.KryptonCheckBox();
            this.panelBoutons = new Krypton.Toolkit.KryptonPanel();
            this.btnAjouter = new Krypton.Toolkit.KryptonButton();
            this.btnModifier = new Krypton.Toolkit.KryptonButton();
            this.btnSupprimer = new Krypton.Toolkit.KryptonButton();
            this.btnEnregistrer = new Krypton.Toolkit.KryptonButton();
            this.btnAnnuler = new Krypton.Toolkit.KryptonButton();
            this.btnActualiser = new Krypton.Toolkit.KryptonButton();
            this.btnFermer = new Krypton.Toolkit.KryptonButton();
            this.statusStrip = new System.Windows.Forms.StatusStrip();
            this.lblStatus = new System.Windows.Forms.ToolStripStatusLabel();
            this.progressBar = new System.Windows.Forms.ToolStripProgressBar();
            ((System.ComponentModel.ISupportInitialize)(this.panelMain)).BeginInit();
            this.panelMain.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainer)).BeginInit();
            this.splitContainer.Panel1.SuspendLayout();
            this.splitContainer.Panel2.SuspendLayout();
            this.splitContainer.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.panelGrille)).BeginInit();
            this.panelGrille.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgvCategories)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelRecherche)).BeginInit();
            this.panelRecherche.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.panelDetails)).BeginInit();
            this.panelDetails.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupBoxDetails)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupBoxDetails.Panel)).BeginInit();
            this.groupBoxDetails.Panel.SuspendLayout();
            this.groupBoxDetails.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cmbCategorieParent)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelBoutons)).BeginInit();
            this.panelBoutons.SuspendLayout();
            this.statusStrip.SuspendLayout();
            this.SuspendLayout();
            // 
            // panelMain
            // 
            this.panelMain.Controls.Add(this.splitContainer);
            this.panelMain.Controls.Add(this.panelBoutons);
            this.panelMain.Controls.Add(this.statusStrip);
            this.panelMain.Dock = System.Windows.Forms.DockStyle.Fill;
            this.panelMain.Location = new System.Drawing.Point(0, 0);
            this.panelMain.Name = "panelMain";
            this.panelMain.Size = new System.Drawing.Size(1200, 700);
            this.panelMain.TabIndex = 0;
            // 
            // splitContainer
            // 
            this.splitContainer.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainer.Location = new System.Drawing.Point(0, 0);
            this.splitContainer.Name = "splitContainer";
            // 
            // splitContainer.Panel1
            // 
            this.splitContainer.Panel1.Controls.Add(this.panelGrille);
            // 
            // splitContainer.Panel2
            // 
            this.splitContainer.Panel2.Controls.Add(this.panelDetails);
            this.splitContainer.Size = new System.Drawing.Size(1200, 630);
            this.splitContainer.SplitterDistance = 700;
            this.splitContainer.TabIndex = 0;
            // 
            // panelGrille
            // 
            this.panelGrille.Controls.Add(this.dgvCategories);
            this.panelGrille.Controls.Add(this.panelRecherche);
            this.panelGrille.Dock = System.Windows.Forms.DockStyle.Fill;
            this.panelGrille.Location = new System.Drawing.Point(0, 0);
            this.panelGrille.Name = "panelGrille";
            this.panelGrille.Size = new System.Drawing.Size(700, 630);
            this.panelGrille.TabIndex = 0;
            // 
            // dgvCategories
            // 
            this.dgvCategories.AllowUserToAddRows = false;
            this.dgvCategories.AllowUserToDeleteRows = false;
            this.dgvCategories.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dgvCategories.Location = new System.Drawing.Point(0, 60);
            this.dgvCategories.Name = "dgvCategories";
            this.dgvCategories.ReadOnly = true;
            this.dgvCategories.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.dgvCategories.Size = new System.Drawing.Size(700, 570);
            this.dgvCategories.TabIndex = 0;
            this.dgvCategories.SelectionChanged += new System.EventHandler(this.dgvCategories_SelectionChanged);
            // 
            // panelRecherche
            // 
            this.panelRecherche.Controls.Add(this.lblRecherche);
            this.panelRecherche.Controls.Add(this.txtRecherche);
            this.panelRecherche.Dock = System.Windows.Forms.DockStyle.Top;
            this.panelRecherche.Location = new System.Drawing.Point(0, 0);
            this.panelRecherche.Name = "panelRecherche";
            this.panelRecherche.Size = new System.Drawing.Size(700, 60);
            this.panelRecherche.TabIndex = 1;
            // 
            // lblRecherche
            // 
            this.lblRecherche.Location = new System.Drawing.Point(12, 20);
            this.lblRecherche.Name = "lblRecherche";
            this.lblRecherche.Size = new System.Drawing.Size(70, 20);
            this.lblRecherche.TabIndex = 0;
            this.lblRecherche.Values.Text = "Recherche:";
            // 
            // txtRecherche
            // 
            this.txtRecherche.Location = new System.Drawing.Point(88, 18);
            this.txtRecherche.Name = "txtRecherche";
            this.txtRecherche.Size = new System.Drawing.Size(300, 23);
            this.txtRecherche.TabIndex = 1;
            this.txtRecherche.TextChanged += new System.EventHandler(this.txtRecherche_TextChanged);
            // 
            // panelDetails
            // 
            this.panelDetails.Controls.Add(this.groupBoxDetails);
            this.panelDetails.Dock = System.Windows.Forms.DockStyle.Fill;
            this.panelDetails.Location = new System.Drawing.Point(0, 0);
            this.panelDetails.Name = "panelDetails";
            this.panelDetails.Padding = new System.Windows.Forms.Padding(10);
            this.panelDetails.Size = new System.Drawing.Size(496, 630);
            this.panelDetails.TabIndex = 0;
            // 
            // groupBoxDetails
            // 
            this.groupBoxDetails.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupBoxDetails.Location = new System.Drawing.Point(10, 10);
            this.groupBoxDetails.Name = "groupBoxDetails";
            // 
            // groupBoxDetails.Panel
            // 
            this.groupBoxDetails.Panel.Controls.Add(this.chkActif);
            this.groupBoxDetails.Panel.Controls.Add(this.cmbCategorieParent);
            this.groupBoxDetails.Panel.Controls.Add(this.lblCategorieParent);
            this.groupBoxDetails.Panel.Controls.Add(this.txtDescription);
            this.groupBoxDetails.Panel.Controls.Add(this.lblDescription);
            this.groupBoxDetails.Panel.Controls.Add(this.txtNom);
            this.groupBoxDetails.Panel.Controls.Add(this.lblNom);
            this.groupBoxDetails.Panel.Controls.Add(this.txtCode);
            this.groupBoxDetails.Panel.Controls.Add(this.lblCode);
            this.groupBoxDetails.Size = new System.Drawing.Size(476, 610);
            this.groupBoxDetails.TabIndex = 0;
            this.groupBoxDetails.Values.Heading = "Détails de la Catégorie";
            // 
            // lblCode
            // 
            this.lblCode.Location = new System.Drawing.Point(20, 30);
            this.lblCode.Name = "lblCode";
            this.lblCode.Size = new System.Drawing.Size(40, 20);
            this.lblCode.TabIndex = 0;
            this.lblCode.Values.Text = "Code:";
            // 
            // txtCode
            // 
            this.txtCode.Location = new System.Drawing.Point(20, 55);
            this.txtCode.Name = "txtCode";
            this.txtCode.ReadOnly = true;
            this.txtCode.Size = new System.Drawing.Size(150, 23);
            this.txtCode.TabIndex = 1;
            // 
            // lblNom
            // 
            this.lblNom.Location = new System.Drawing.Point(20, 90);
            this.lblNom.Name = "lblNom";
            this.lblNom.Size = new System.Drawing.Size(38, 20);
            this.lblNom.TabIndex = 2;
            this.lblNom.Values.Text = "Nom:";
            // 
            // txtNom
            // 
            this.txtNom.Location = new System.Drawing.Point(20, 115);
            this.txtNom.Name = "txtNom";
            this.txtNom.ReadOnly = true;
            this.txtNom.Size = new System.Drawing.Size(400, 23);
            this.txtNom.TabIndex = 3;
            // 
            // lblDescription
            // 
            this.lblDescription.Location = new System.Drawing.Point(20, 150);
            this.lblDescription.Name = "lblDescription";
            this.lblDescription.Size = new System.Drawing.Size(74, 20);
            this.lblDescription.TabIndex = 4;
            this.lblDescription.Values.Text = "Description:";
            // 
            // txtDescription
            // 
            this.txtDescription.Location = new System.Drawing.Point(20, 175);
            this.txtDescription.Multiline = true;
            this.txtDescription.Name = "txtDescription";
            this.txtDescription.ReadOnly = true;
            this.txtDescription.Size = new System.Drawing.Size(400, 80);
            this.txtDescription.TabIndex = 5;
            // 
            // lblCategorieParent
            // 
            this.lblCategorieParent.Location = new System.Drawing.Point(20, 270);
            this.lblCategorieParent.Name = "lblCategorieParent";
            this.lblCategorieParent.Size = new System.Drawing.Size(105, 20);
            this.lblCategorieParent.TabIndex = 6;
            this.lblCategorieParent.Values.Text = "Catégorie Parent:";
            // 
            // cmbCategorieParent
            // 
            this.cmbCategorieParent.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmbCategorieParent.DropDownWidth = 400;
            this.cmbCategorieParent.Enabled = false;
            this.cmbCategorieParent.Location = new System.Drawing.Point(20, 295);
            this.cmbCategorieParent.Name = "cmbCategorieParent";
            this.cmbCategorieParent.Size = new System.Drawing.Size(400, 21);
            this.cmbCategorieParent.TabIndex = 7;
            // 
            // chkActif
            // 
            this.chkActif.Enabled = false;
            this.chkActif.Location = new System.Drawing.Point(20, 330);
            this.chkActif.Name = "chkActif";
            this.chkActif.Size = new System.Drawing.Size(50, 20);
            this.chkActif.TabIndex = 8;
            this.chkActif.Values.Text = "Actif";
            // 
            // panelBoutons
            // 
            this.panelBoutons.Controls.Add(this.btnFermer);
            this.panelBoutons.Controls.Add(this.btnActualiser);
            this.panelBoutons.Controls.Add(this.btnAnnuler);
            this.panelBoutons.Controls.Add(this.btnEnregistrer);
            this.panelBoutons.Controls.Add(this.btnSupprimer);
            this.panelBoutons.Controls.Add(this.btnModifier);
            this.panelBoutons.Controls.Add(this.btnAjouter);
            this.panelBoutons.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.panelBoutons.Location = new System.Drawing.Point(0, 630);
            this.panelBoutons.Name = "panelBoutons";
            this.panelBoutons.Size = new System.Drawing.Size(1200, 48);
            this.panelBoutons.TabIndex = 1;
            // 
            // btnAjouter
            // 
            this.btnAjouter.Location = new System.Drawing.Point(12, 8);
            this.btnAjouter.Name = "btnAjouter";
            this.btnAjouter.Size = new System.Drawing.Size(90, 32);
            this.btnAjouter.TabIndex = 0;
            this.btnAjouter.Values.Text = "➕ Ajouter";
            this.btnAjouter.Click += new System.EventHandler(this.btnAjouter_Click);
            // 
            // btnModifier
            // 
            this.btnModifier.Location = new System.Drawing.Point(108, 8);
            this.btnModifier.Name = "btnModifier";
            this.btnModifier.Size = new System.Drawing.Size(90, 32);
            this.btnModifier.TabIndex = 1;
            this.btnModifier.Values.Text = "✏️ Modifier";
            this.btnModifier.Click += new System.EventHandler(this.btnModifier_Click);
            // 
            // btnSupprimer
            // 
            this.btnSupprimer.Location = new System.Drawing.Point(204, 8);
            this.btnSupprimer.Name = "btnSupprimer";
            this.btnSupprimer.Size = new System.Drawing.Size(90, 32);
            this.btnSupprimer.TabIndex = 2;
            this.btnSupprimer.Values.Text = "🗑️ Supprimer";
            this.btnSupprimer.Click += new System.EventHandler(this.btnSupprimer_Click);
            // 
            // btnEnregistrer
            // 
            this.btnEnregistrer.Location = new System.Drawing.Point(320, 8);
            this.btnEnregistrer.Name = "btnEnregistrer";
            this.btnEnregistrer.Size = new System.Drawing.Size(100, 32);
            this.btnEnregistrer.TabIndex = 3;
            this.btnEnregistrer.Values.Text = "💾 Enregistrer";
            this.btnEnregistrer.Click += new System.EventHandler(this.btnEnregistrer_Click);
            // 
            // btnAnnuler
            // 
            this.btnAnnuler.Location = new System.Drawing.Point(426, 8);
            this.btnAnnuler.Name = "btnAnnuler";
            this.btnAnnuler.Size = new System.Drawing.Size(90, 32);
            this.btnAnnuler.TabIndex = 4;
            this.btnAnnuler.Values.Text = "❌ Annuler";
            this.btnAnnuler.Click += new System.EventHandler(this.btnAnnuler_Click);
            // 
            // btnActualiser
            // 
            this.btnActualiser.Location = new System.Drawing.Point(542, 8);
            this.btnActualiser.Name = "btnActualiser";
            this.btnActualiser.Size = new System.Drawing.Size(90, 32);
            this.btnActualiser.TabIndex = 5;
            this.btnActualiser.Values.Text = "🔄 Actualiser";
            this.btnActualiser.Click += new System.EventHandler(this.btnActualiser_Click);
            // 
            // btnFermer
            // 
            this.btnFermer.Location = new System.Drawing.Point(1098, 8);
            this.btnFermer.Name = "btnFermer";
            this.btnFermer.Size = new System.Drawing.Size(90, 32);
            this.btnFermer.TabIndex = 6;
            this.btnFermer.Values.Text = "🚪 Fermer";
            this.btnFermer.Click += new System.EventHandler(this.btnFermer_Click);
            // 
            // statusStrip
            // 
            this.statusStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.lblStatus,
            this.progressBar});
            this.statusStrip.Location = new System.Drawing.Point(0, 678);
            this.statusStrip.Name = "statusStrip";
            this.statusStrip.Size = new System.Drawing.Size(1200, 22);
            this.statusStrip.TabIndex = 2;
            this.statusStrip.Text = "statusStrip1";
            // 
            // lblStatus
            // 
            this.lblStatus.Name = "lblStatus";
            this.lblStatus.Size = new System.Drawing.Size(26, 17);
            this.lblStatus.Text = "Prêt";
            // 
            // progressBar
            // 
            this.progressBar.Name = "progressBar";
            this.progressBar.Size = new System.Drawing.Size(100, 16);
            this.progressBar.Visible = false;
            // 
            // FRM_CATEGORIES
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1200, 700);
            this.Controls.Add(this.panelMain);
            this.Name = "FRM_CATEGORIES";
            this.Text = "Gestion des Catégories";
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.FRM_CATEGORIES_FormClosing);
            ((System.ComponentModel.ISupportInitialize)(this.panelMain)).EndInit();
            this.panelMain.ResumeLayout(false);
            this.panelMain.PerformLayout();
            this.splitContainer.Panel1.ResumeLayout(false);
            this.splitContainer.Panel2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainer)).EndInit();
            this.splitContainer.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.panelGrille)).EndInit();
            this.panelGrille.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.dgvCategories)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelRecherche)).EndInit();
            this.panelRecherche.ResumeLayout(false);
            this.panelRecherche.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.panelDetails)).EndInit();
            this.panelDetails.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupBoxDetails.Panel)).EndInit();
            this.groupBoxDetails.Panel.ResumeLayout(false);
            this.groupBoxDetails.Panel.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupBoxDetails)).EndInit();
            this.groupBoxDetails.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.cmbCategorieParent)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelBoutons)).EndInit();
            this.panelBoutons.ResumeLayout(false);
            this.statusStrip.ResumeLayout(false);
            this.statusStrip.PerformLayout();
            this.ResumeLayout(false);
        }

        #endregion

        private Krypton.Toolkit.KryptonPanel panelMain;
        private System.Windows.Forms.SplitContainer splitContainer;
        private Krypton.Toolkit.KryptonPanel panelGrille;
        private Krypton.Toolkit.KryptonDataGridView dgvCategories;
        private Krypton.Toolkit.KryptonPanel panelRecherche;
        private Krypton.Toolkit.KryptonLabel lblRecherche;
        private Krypton.Toolkit.KryptonTextBox txtRecherche;
        private Krypton.Toolkit.KryptonPanel panelDetails;
        private Krypton.Toolkit.KryptonGroupBox groupBoxDetails;
        private Krypton.Toolkit.KryptonLabel lblCode;
        private Krypton.Toolkit.KryptonTextBox txtCode;
        private Krypton.Toolkit.KryptonLabel lblNom;
        private Krypton.Toolkit.KryptonTextBox txtNom;
        private Krypton.Toolkit.KryptonLabel lblDescription;
        private Krypton.Toolkit.KryptonTextBox txtDescription;
        private Krypton.Toolkit.KryptonLabel lblCategorieParent;
        private Krypton.Toolkit.KryptonComboBox cmbCategorieParent;
        private Krypton.Toolkit.KryptonCheckBox chkActif;
        private Krypton.Toolkit.KryptonPanel panelBoutons;
        private Krypton.Toolkit.KryptonButton btnAjouter;
        private Krypton.Toolkit.KryptonButton btnModifier;
        private Krypton.Toolkit.KryptonButton btnSupprimer;
        private Krypton.Toolkit.KryptonButton btnEnregistrer;
        private Krypton.Toolkit.KryptonButton btnAnnuler;
        private Krypton.Toolkit.KryptonButton btnActualiser;
        private Krypton.Toolkit.KryptonButton btnFermer;
        private System.Windows.Forms.StatusStrip statusStrip;
        private System.Windows.Forms.ToolStripStatusLabel lblStatus;
        private System.Windows.Forms.ToolStripProgressBar progressBar;
    }
}

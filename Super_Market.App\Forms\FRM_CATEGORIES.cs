using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using Krypton.Toolkit;
using Super_Market.Business.Services;
using Super_Market.Core.Models;

namespace Super_Market.App.Forms
{
    public partial class FRM_CATEGORIES : KryptonForm
    {
        private readonly CategorieService _categorieService;
        private readonly Utilisateur _utilisateurConnecte;
        private List<Categorie> _categories;
        private Categorie _categorieSelectionnee;
        private bool _modeEdition = false;

        public FRM_CATEGORIES(Utilisateur utilisateur)
        {
            InitializeComponent();
            _categorieService = new CategorieService();
            _utilisateurConnecte = utilisateur;
            InitialiserFormulaire();
        }

        private void InitialiserFormulaire()
        {
            this.Text = "Gestion des Catégories";
            this.WindowState = FormWindowState.Maximized;
            
            // Configurer la grille
            ConfigurerGrille();
            
            // Charger les données
            ChargerCategories();
            
            // État initial des boutons
            ActiverBoutons(false);
            btnAjouter.Enabled = true;
            btnActualiser.Enabled = true;
        }

        private void ConfigurerGrille()
        {
            dgvCategories.AutoGenerateColumns = false;
            dgvCategories.AllowUserToAddRows = false;
            dgvCategories.AllowUserToDeleteRows = false;
            dgvCategories.ReadOnly = true;
            dgvCategories.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvCategories.MultiSelect = false;

            dgvCategories.Columns.Clear();
            
            dgvCategories.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Id",
                HeaderText = "ID",
                DataPropertyName = "Id",
                Width = 60,
                Visible = false
            });

            dgvCategories.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Code",
                HeaderText = "Code",
                DataPropertyName = "Code",
                Width = 80
            });

            dgvCategories.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Nom",
                HeaderText = "Nom de la Catégorie",
                DataPropertyName = "Nom",
                Width = 200
            });

            dgvCategories.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Description",
                HeaderText = "Description",
                DataPropertyName = "Description",
                Width = 300
            });

            dgvCategories.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "CategorieParent",
                HeaderText = "Catégorie Parent",
                DataPropertyName = "CategorieParentNom",
                Width = 150
            });

            dgvCategories.Columns.Add(new DataGridViewCheckBoxColumn
            {
                Name = "EstActif",
                HeaderText = "Actif",
                DataPropertyName = "EstActif",
                Width = 60
            });
        }

        private async void ChargerCategories()
        {
            try
            {
                lblStatus.Text = "Chargement des catégories...";
                progressBar.Visible = true;
                
                _categories = (await _categorieService.GetAllCategoriesAsync()).ToList();
                
                // Enrichir avec les noms des catégories parentes
                foreach (var categorie in _categories)
                {
                    if (categorie.CategorieParentId.HasValue)
                    {
                        var parent = _categories.FirstOrDefault(c => c.Id == categorie.CategorieParentId.Value);
                        if (parent != null)
                        {
                            // Utiliser une propriété dynamique pour l'affichage
                            ((dynamic)categorie).CategorieParentNom = parent.Nom;
                        }
                    }
                }
                
                dgvCategories.DataSource = _categories;
                
                // Charger les catégories parentes dans le ComboBox
                ChargerCategoriesParentes();
                
                lblStatus.Text = $"{_categories.Count} catégorie(s) chargée(s)";
                progressBar.Visible = false;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors du chargement des catégories: {ex.Message}",
                              "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                lblStatus.Text = "Erreur de chargement";
                progressBar.Visible = false;
            }
        }

        private async void ChargerCategoriesParentes()
        {
            try
            {
                var categoriesPrincipales = await _categorieService.GetCategoriesPrincipalesAsync();
                
                cmbCategorieParent.Items.Clear();
                cmbCategorieParent.Items.Add(new { Id = (int?)null, Nom = "Aucune (Catégorie principale)" });
                
                foreach (var categorie in categoriesPrincipales)
                {
                    cmbCategorieParent.Items.Add(new { Id = (int?)categorie.Id, Nom = categorie.Nom });
                }
                
                cmbCategorieParent.DisplayMember = "Nom";
                cmbCategorieParent.ValueMember = "Id";
                cmbCategorieParent.SelectedIndex = 0;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors du chargement des catégories parentes: {ex.Message}",
                              "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void dgvCategories_SelectionChanged(object sender, EventArgs e)
        {
            if (dgvCategories.SelectedRows.Count > 0)
            {
                var index = dgvCategories.SelectedRows[0].Index;
                if (index >= 0 && index < _categories.Count)
                {
                    _categorieSelectionnee = _categories[index];
                    AfficherCategorie(_categorieSelectionnee);
                    ActiverBoutons(true);
                }
            }
            else
            {
                _categorieSelectionnee = null;
                ViderChamps();
                ActiverBoutons(false);
            }
        }

        private void AfficherCategorie(Categorie categorie)
        {
            if (categorie != null)
            {
                txtCode.Text = categorie.Code ?? "";
                txtNom.Text = categorie.Nom ?? "";
                txtDescription.Text = categorie.Description ?? "";
                chkActif.Checked = categorie.EstActif;
                
                // Sélectionner la catégorie parente
                if (categorie.CategorieParentId.HasValue)
                {
                    for (int i = 0; i < cmbCategorieParent.Items.Count; i++)
                    {
                        var item = (dynamic)cmbCategorieParent.Items[i];
                        if (item.Id == categorie.CategorieParentId.Value)
                        {
                            cmbCategorieParent.SelectedIndex = i;
                            break;
                        }
                    }
                }
                else
                {
                    cmbCategorieParent.SelectedIndex = 0;
                }
            }
        }

        private void ViderChamps()
        {
            txtCode.Text = "";
            txtNom.Text = "";
            txtDescription.Text = "";
            chkActif.Checked = true;
            cmbCategorieParent.SelectedIndex = 0;
        }

        private void ActiverBoutons(bool categorieSelectionnee)
        {
            btnModifier.Enabled = categorieSelectionnee && !_modeEdition;
            btnSupprimer.Enabled = categorieSelectionnee && !_modeEdition;
            btnEnregistrer.Enabled = _modeEdition;
            btnAnnuler.Enabled = _modeEdition;
        }

        private void ActiverModeEdition(bool activer)
        {
            _modeEdition = activer;
            
            txtCode.ReadOnly = !activer;
            txtNom.ReadOnly = !activer;
            txtDescription.ReadOnly = !activer;
            chkActif.Enabled = activer;
            cmbCategorieParent.Enabled = activer;
            
            dgvCategories.Enabled = !activer;
            
            ActiverBoutons(_categorieSelectionnee != null);
        }

        private async void btnAjouter_Click(object sender, EventArgs e)
        {
            _categorieSelectionnee = null;
            ViderChamps();
            ActiverModeEdition(true);
            txtCode.Focus();
            
            // Générer un code automatique
            try
            {
                txtCode.Text = await _categorieService.GenererCodeAutomatiqueAsync();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors de la génération du code: {ex.Message}",
                              "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        private void btnModifier_Click(object sender, EventArgs e)
        {
            if (_categorieSelectionnee != null)
            {
                ActiverModeEdition(true);
                txtNom.Focus();
            }
        }

        private async void btnEnregistrer_Click(object sender, EventArgs e)
        {
            await EnregistrerCategorie();
        }

        private async Task EnregistrerCategorie()
        {
            try
            {
                // Validation
                if (string.IsNullOrWhiteSpace(txtNom.Text))
                {
                    MessageBox.Show("Le nom de la catégorie est obligatoire.",
                                  "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtNom.Focus();
                    return;
                }

                var categorie = _categorieSelectionnee ?? new Categorie();
                
                categorie.Code = txtCode.Text.Trim();
                categorie.Nom = txtNom.Text.Trim();
                categorie.Description = txtDescription.Text.Trim();
                categorie.EstActif = chkActif.Checked;
                
                // Catégorie parente
                var parentItem = (dynamic)cmbCategorieParent.SelectedItem;
                categorie.CategorieParentId = parentItem?.Id;
                
                if (_categorieSelectionnee == null)
                {
                    // Nouveau
                    categorie.DateCreation = DateTime.Now;
                    categorie.UtilisateurCreation = _utilisateurConnecte.NomUtilisateur;
                    
                    await _categorieService.AjouterCategorieAsync(categorie);
                    MessageBox.Show("Catégorie ajoutée avec succès !",
                                  "Succès", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    // Modification
                    categorie.DateModification = DateTime.Now;
                    categorie.UtilisateurModification = _utilisateurConnecte.NomUtilisateur;
                    
                    await _categorieService.ModifierCategorieAsync(categorie);
                    MessageBox.Show("Catégorie modifiée avec succès !",
                                  "Succès", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }

                ActiverModeEdition(false);
                ChargerCategories();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors de l'enregistrement: {ex.Message}",
                              "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnAnnuler_Click(object sender, EventArgs e)
        {
            ActiverModeEdition(false);
            if (_categorieSelectionnee != null)
            {
                AfficherCategorie(_categorieSelectionnee);
            }
            else
            {
                ViderChamps();
            }
        }

        private async void btnSupprimer_Click(object sender, EventArgs e)
        {
            if (_categorieSelectionnee != null)
            {
                var result = MessageBox.Show(
                    $"Êtes-vous sûr de vouloir supprimer la catégorie '{_categorieSelectionnee.Nom}' ?",
                    "Confirmation de suppression",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    try
                    {
                        await _categorieService.SupprimerCategorieAsync(_categorieSelectionnee.Id);
                        MessageBox.Show("Catégorie supprimée avec succès !",
                                      "Succès", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        ChargerCategories();
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"Erreur lors de la suppression: {ex.Message}",
                                      "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
        }

        private void btnActualiser_Click(object sender, EventArgs e)
        {
            ChargerCategories();
        }

        private void btnFermer_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void txtRecherche_TextChanged(object sender, EventArgs e)
        {
            FiltrerCategories();
        }

        private void FiltrerCategories()
        {
            if (_categories == null) return;

            var termeRecherche = txtRecherche.Text.ToLower();
            
            if (string.IsNullOrWhiteSpace(termeRecherche))
            {
                dgvCategories.DataSource = _categories;
            }
            else
            {
                var categoriesFiltrees = _categories.Where(c =>
                    (c.Code?.ToLower().Contains(termeRecherche) ?? false) ||
                    (c.Nom?.ToLower().Contains(termeRecherche) ?? false) ||
                    (c.Description?.ToLower().Contains(termeRecherche) ?? false)
                ).ToList();
                
                dgvCategories.DataSource = categoriesFiltrees;
            }
        }

        private void FRM_CATEGORIES_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (_modeEdition)
            {
                var result = MessageBox.Show(
                    "Des modifications sont en cours. Voulez-vous les abandonner ?",
                    "Modifications en cours",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result == DialogResult.No)
                {
                    e.Cancel = true;
                }
            }
        }
    }
}

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using Krypton.Toolkit;
using Super_Market.Business.Services;
using Super_Market.Core.Models;

namespace Super_Market.App.Forms
{
    public partial class FRM_CATEGORIES_LIST : KryptonForm
    {
        private readonly CategorieService _categorieService;
        private readonly Utilisateur _utilisateurConnecte;
        private List<Categorie> _categories;
        private Categorie _categorieSelectionnee;

        public FRM_CATEGORIES_LIST(Utilisateur utilisateur)
        {
            InitializeComponent();
            _categorieService = new CategorieService();
            _utilisateurConnecte = utilisateur;
            InitialiserFormulaire();
        }

        private void InitialiserFormulaire()
        {
            this.Text = "Liste des Catégories";
            this.WindowState = FormWindowState.Maximized;
            
            // Configurer la grille
            ConfigurerGrille();
            
            // Charger les données
            ChargerCategories();
        }

        private void ConfigurerGrille()
        {
            dgvCategories.AutoGenerateColumns = false;
            dgvCategories.AllowUserToAddRows = false;
            dgvCategories.AllowUserToDeleteRows = false;
            dgvCategories.ReadOnly = true;
            dgvCategories.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvCategories.MultiSelect = false;

            dgvCategories.Columns.Clear();
            
            dgvCategories.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Id",
                HeaderText = "ID",
                DataPropertyName = "Id",
                Width = 60,
                Visible = false
            });

            dgvCategories.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Code",
                HeaderText = "Code",
                DataPropertyName = "Code",
                Width = 100
            });

            dgvCategories.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Nom",
                HeaderText = "Nom de la Catégorie",
                DataPropertyName = "Nom",
                Width = 250
            });

            dgvCategories.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Description",
                HeaderText = "Description",
                DataPropertyName = "Description",
                Width = 300
            });

            dgvCategories.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "CategorieParent",
                HeaderText = "Catégorie Parent",
                DataPropertyName = "CategorieParentNom",
                Width = 200
            });

            dgvCategories.Columns.Add(new DataGridViewCheckBoxColumn
            {
                Name = "EstActif",
                HeaderText = "Actif",
                DataPropertyName = "EstActif",
                Width = 80
            });

            dgvCategories.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "DateCreation",
                HeaderText = "Date Création",
                DataPropertyName = "DateCreation",
                Width = 120,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "dd/MM/yyyy" }
            });
        }

        private async void ChargerCategories()
        {
            try
            {
                lblStatus.Text = "Chargement des catégories...";
                progressBar.Visible = true;
                btnActualiser.Enabled = false;
                
                _categories = (await _categorieService.GetAllCategoriesAsync()).ToList();
                
                // Enrichir avec les noms des catégories parentes
                foreach (var categorie in _categories)
                {
                    if (categorie.CategorieParentId.HasValue)
                    {
                        var parent = _categories.FirstOrDefault(c => c.Id == categorie.CategorieParentId.Value);
                        if (parent != null)
                        {
                            // Utiliser une propriété dynamique pour l'affichage
                            ((dynamic)categorie).CategorieParentNom = parent.Nom;
                        }
                    }
                    else
                    {
                        ((dynamic)categorie).CategorieParentNom = "Aucune";
                    }
                }
                
                dgvCategories.DataSource = _categories;
                
                lblStatus.Text = $"{_categories.Count} catégorie(s) chargée(s)";
                progressBar.Visible = false;
                btnActualiser.Enabled = true;
                
                // Activer/désactiver les boutons selon la sélection
                ActiverBoutons();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors du chargement des catégories: {ex.Message}",
                              "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                lblStatus.Text = "Erreur de chargement";
                progressBar.Visible = false;
                btnActualiser.Enabled = true;
            }
        }

        private void dgvCategories_SelectionChanged(object sender, EventArgs e)
        {
            if (dgvCategories.SelectedRows.Count > 0)
            {
                var index = dgvCategories.SelectedRows[0].Index;
                if (index >= 0 && index < _categories.Count)
                {
                    _categorieSelectionnee = _categories[index];
                }
            }
            else
            {
                _categorieSelectionnee = null;
            }
            
            ActiverBoutons();
        }

        private void ActiverBoutons()
        {
            bool categorieSelectionnee = _categorieSelectionnee != null;
            btnModifier.Enabled = categorieSelectionnee;
            btnSupprimer.Enabled = categorieSelectionnee;
            btnVoir.Enabled = categorieSelectionnee;
        }

        private void btnAjouter_Click(object sender, EventArgs e)
        {
            using (var formAjout = new FRM_CATEGORIE_EDIT(_utilisateurConnecte))
            {
                if (formAjout.ShowDialog() == DialogResult.OK)
                {
                    ChargerCategories();
                }
            }
        }

        private void btnModifier_Click(object sender, EventArgs e)
        {
            if (_categorieSelectionnee != null)
            {
                using (var formModif = new FRM_CATEGORIE_EDIT(_utilisateurConnecte, _categorieSelectionnee))
                {
                    if (formModif.ShowDialog() == DialogResult.OK)
                    {
                        ChargerCategories();
                    }
                }
            }
        }

        private void btnVoir_Click(object sender, EventArgs e)
        {
            if (_categorieSelectionnee != null)
            {
                using (var formVoir = new FRM_CATEGORIE_EDIT(_utilisateurConnecte, _categorieSelectionnee, true))
                {
                    formVoir.ShowDialog();
                }
            }
        }

        private async void btnSupprimer_Click(object sender, EventArgs e)
        {
            if (_categorieSelectionnee != null)
            {
                var result = MessageBox.Show(
                    $"Êtes-vous sûr de vouloir supprimer la catégorie '{_categorieSelectionnee.Nom}' ?\n\n" +
                    "Cette action est irréversible.",
                    "Confirmation de suppression",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    try
                    {
                        lblStatus.Text = "Suppression en cours...";
                        await _categorieService.SupprimerCategorieAsync(_categorieSelectionnee.Id);
                        
                        MessageBox.Show("Catégorie supprimée avec succès !",
                                      "Succès", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        
                        ChargerCategories();
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"Erreur lors de la suppression: {ex.Message}",
                                      "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        lblStatus.Text = "Erreur de suppression";
                    }
                }
            }
        }

        private void btnActualiser_Click(object sender, EventArgs e)
        {
            ChargerCategories();
        }

        private void btnFermer_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void txtRecherche_TextChanged(object sender, EventArgs e)
        {
            FiltrerCategories();
        }

        private void FiltrerCategories()
        {
            if (_categories == null) return;

            var termeRecherche = txtRecherche.Text.ToLower();
            
            if (string.IsNullOrWhiteSpace(termeRecherche))
            {
                dgvCategories.DataSource = _categories;
            }
            else
            {
                var categoriesFiltrees = _categories.Where(c =>
                    (c.Code?.ToLower().Contains(termeRecherche) ?? false) ||
                    (c.Nom?.ToLower().Contains(termeRecherche) ?? false) ||
                    (c.Description?.ToLower().Contains(termeRecherche) ?? false)
                ).ToList();
                
                dgvCategories.DataSource = categoriesFiltrees;
            }
            
            lblStatus.Text = $"{((List<Categorie>)dgvCategories.DataSource).Count} catégorie(s) affichée(s)";
        }

        private void dgvCategories_DoubleClick(object sender, EventArgs e)
        {
            if (_categorieSelectionnee != null)
            {
                btnModifier_Click(sender, e);
            }
        }

        private void FRM_CATEGORIES_LIST_Load(object sender, EventArgs e)
        {
            // Focus sur la recherche
            txtRecherche.Focus();
        }

        private void dgvCategories_KeyDown(object sender, KeyEventArgs e)
        {
            switch (e.KeyCode)
            {
                case Keys.Enter:
                    if (_categorieSelectionnee != null)
                    {
                        btnModifier_Click(sender, e);
                        e.Handled = true;
                    }
                    break;
                    
                case Keys.Delete:
                    if (_categorieSelectionnee != null)
                    {
                        btnSupprimer_Click(sender, e);
                        e.Handled = true;
                    }
                    break;
                    
                case Keys.F5:
                    btnActualiser_Click(sender, e);
                    e.Handled = true;
                    break;
            }
        }

        private void btnExporter_Click(object sender, EventArgs e)
        {
            try
            {
                if (_categories == null || _categories.Count == 0)
                {
                    MessageBox.Show("Aucune donnée à exporter.", "Information", 
                                  MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                using (var saveDialog = new SaveFileDialog())
                {
                    saveDialog.Filter = "Fichiers CSV (*.csv)|*.csv|Tous les fichiers (*.*)|*.*";
                    saveDialog.Title = "Exporter les catégories";
                    saveDialog.FileName = $"Categories_{DateTime.Now:yyyyMMdd_HHmmss}.csv";

                    if (saveDialog.ShowDialog() == DialogResult.OK)
                    {
                        ExporterVersCsv(saveDialog.FileName);
                        MessageBox.Show("Export réalisé avec succès !", "Succès", 
                                      MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors de l'export: {ex.Message}", "Erreur", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ExporterVersCsv(string cheminFichier)
        {
            using (var writer = new System.IO.StreamWriter(cheminFichier, false, System.Text.Encoding.UTF8))
            {
                // En-têtes
                writer.WriteLine("Code;Nom;Description;Catégorie Parent;Actif;Date Création");
                
                // Données
                foreach (var categorie in _categories)
                {
                    var parentNom = "";
                    if (categorie.CategorieParentId.HasValue)
                    {
                        var parent = _categories.FirstOrDefault(c => c.Id == categorie.CategorieParentId.Value);
                        parentNom = parent?.Nom ?? "";
                    }
                    
                    writer.WriteLine($"{categorie.Code};{categorie.Nom};{categorie.Description};" +
                                   $"{parentNom};{(categorie.EstActif ? "Oui" : "Non")};" +
                                   $"{categorie.DateCreation:dd/MM/yyyy}");
                }
            }
        }
    }
}

namespace Super_Market.App.Forms
{
    partial class FRM_CATEGORIE_EDIT
    {
        private System.ComponentModel.IContainer components = null;

        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        private void InitializeComponent()
        {
            this.panelMain = new Krypton.Toolkit.KryptonPanel();
            this.groupBoxDetails = new Krypton.Toolkit.KryptonGroupBox();
            this.lblCode = new Krypton.Toolkit.KryptonLabel();
            this.txtCode = new Krypton.Toolkit.KryptonTextBox();
            this.lblNom = new Krypton.Toolkit.KryptonLabel();
            this.txtNom = new Krypton.Toolkit.KryptonTextBox();
            this.lblDescription = new Krypton.Toolkit.KryptonLabel();
            this.txtDescription = new Krypton.Toolkit.KryptonTextBox();
            this.lblCategorieParent = new Krypton.Toolkit.KryptonLabel();
            this.cmbCategorieParent = new Krypton.Toolkit.KryptonComboBox();
            this.chkActif = new Krypton.Toolkit.KryptonCheckBox();
            this.panelInfo = new Krypton.Toolkit.KryptonPanel();
            this.lblInfoCreation = new Krypton.Toolkit.KryptonLabel();
            this.lblInfoModification = new Krypton.Toolkit.KryptonLabel();
            this.panelBoutons = new Krypton.Toolkit.KryptonPanel();
            this.btnEnregistrer = new Krypton.Toolkit.KryptonButton();
            this.btnAnnuler = new Krypton.Toolkit.KryptonButton();
            ((System.ComponentModel.ISupportInitialize)(this.panelMain)).BeginInit();
            this.panelMain.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupBoxDetails)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupBoxDetails.Panel)).BeginInit();
            this.groupBoxDetails.Panel.SuspendLayout();
            this.groupBoxDetails.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cmbCategorieParent)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelInfo)).BeginInit();
            this.panelInfo.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.panelBoutons)).BeginInit();
            this.panelBoutons.SuspendLayout();
            this.SuspendLayout();
            // 
            // panelMain
            // 
            this.panelMain.Controls.Add(this.groupBoxDetails);
            this.panelMain.Controls.Add(this.panelInfo);
            this.panelMain.Controls.Add(this.panelBoutons);
            this.panelMain.Dock = System.Windows.Forms.DockStyle.Fill;
            this.panelMain.Location = new System.Drawing.Point(0, 0);
            this.panelMain.Name = "panelMain";
            this.panelMain.Padding = new System.Windows.Forms.Padding(10);
            this.panelMain.Size = new System.Drawing.Size(600, 500);
            this.panelMain.TabIndex = 0;
            // 
            // groupBoxDetails
            // 
            this.groupBoxDetails.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupBoxDetails.Location = new System.Drawing.Point(10, 10);
            this.groupBoxDetails.Name = "groupBoxDetails";
            // 
            // groupBoxDetails.Panel
            // 
            this.groupBoxDetails.Panel.Controls.Add(this.chkActif);
            this.groupBoxDetails.Panel.Controls.Add(this.cmbCategorieParent);
            this.groupBoxDetails.Panel.Controls.Add(this.lblCategorieParent);
            this.groupBoxDetails.Panel.Controls.Add(this.txtDescription);
            this.groupBoxDetails.Panel.Controls.Add(this.lblDescription);
            this.groupBoxDetails.Panel.Controls.Add(this.txtNom);
            this.groupBoxDetails.Panel.Controls.Add(this.lblNom);
            this.groupBoxDetails.Panel.Controls.Add(this.txtCode);
            this.groupBoxDetails.Panel.Controls.Add(this.lblCode);
            this.groupBoxDetails.Size = new System.Drawing.Size(580, 370);
            this.groupBoxDetails.TabIndex = 0;
            this.groupBoxDetails.Values.Heading = "Informations de la Catégorie";
            // 
            // lblCode
            // 
            this.lblCode.Location = new System.Drawing.Point(20, 20);
            this.lblCode.Name = "lblCode";
            this.lblCode.Size = new System.Drawing.Size(40, 20);
            this.lblCode.TabIndex = 0;
            this.lblCode.Values.Text = "Code:";
            // 
            // txtCode
            // 
            this.txtCode.Location = new System.Drawing.Point(20, 45);
            this.txtCode.Name = "txtCode";
            this.txtCode.Size = new System.Drawing.Size(150, 23);
            this.txtCode.TabIndex = 1;
            // 
            // lblNom
            // 
            this.lblNom.Location = new System.Drawing.Point(20, 80);
            this.lblNom.Name = "lblNom";
            this.lblNom.Size = new System.Drawing.Size(38, 20);
            this.lblNom.TabIndex = 2;
            this.lblNom.Values.Text = "Nom: *";
            // 
            // txtNom
            // 
            this.txtNom.Location = new System.Drawing.Point(20, 105);
            this.txtNom.Name = "txtNom";
            this.txtNom.Size = new System.Drawing.Size(520, 23);
            this.txtNom.TabIndex = 3;
            this.txtNom.KeyDown += new System.Windows.Forms.KeyEventHandler(this.txtNom_KeyDown);
            // 
            // lblDescription
            // 
            this.lblDescription.Location = new System.Drawing.Point(20, 140);
            this.lblDescription.Name = "lblDescription";
            this.lblDescription.Size = new System.Drawing.Size(74, 20);
            this.lblDescription.TabIndex = 4;
            this.lblDescription.Values.Text = "Description:";
            // 
            // txtDescription
            // 
            this.txtDescription.Location = new System.Drawing.Point(20, 165);
            this.txtDescription.Multiline = true;
            this.txtDescription.Name = "txtDescription";
            this.txtDescription.Size = new System.Drawing.Size(520, 80);
            this.txtDescription.TabIndex = 5;
            this.txtDescription.KeyDown += new System.Windows.Forms.KeyEventHandler(this.txtDescription_KeyDown);
            // 
            // lblCategorieParent
            // 
            this.lblCategorieParent.Location = new System.Drawing.Point(20, 260);
            this.lblCategorieParent.Name = "lblCategorieParent";
            this.lblCategorieParent.Size = new System.Drawing.Size(105, 20);
            this.lblCategorieParent.TabIndex = 6;
            this.lblCategorieParent.Values.Text = "Catégorie Parent:";
            // 
            // cmbCategorieParent
            // 
            this.cmbCategorieParent.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmbCategorieParent.DropDownWidth = 520;
            this.cmbCategorieParent.Location = new System.Drawing.Point(20, 285);
            this.cmbCategorieParent.Name = "cmbCategorieParent";
            this.cmbCategorieParent.Size = new System.Drawing.Size(520, 21);
            this.cmbCategorieParent.TabIndex = 7;
            // 
            // chkActif
            // 
            this.chkActif.Checked = true;
            this.chkActif.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkActif.Location = new System.Drawing.Point(20, 320);
            this.chkActif.Name = "chkActif";
            this.chkActif.Size = new System.Drawing.Size(50, 20);
            this.chkActif.TabIndex = 8;
            this.chkActif.Values.Text = "Actif";
            // 
            // panelInfo
            // 
            this.panelInfo.Controls.Add(this.lblInfoModification);
            this.panelInfo.Controls.Add(this.lblInfoCreation);
            this.panelInfo.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.panelInfo.Location = new System.Drawing.Point(10, 380);
            this.panelInfo.Name = "panelInfo";
            this.panelInfo.Size = new System.Drawing.Size(580, 60);
            this.panelInfo.TabIndex = 1;
            // 
            // lblInfoCreation
            // 
            this.lblInfoCreation.Location = new System.Drawing.Point(10, 10);
            this.lblInfoCreation.Name = "lblInfoCreation";
            this.lblInfoCreation.Size = new System.Drawing.Size(6, 2);
            this.lblInfoCreation.StateCommon.ShortText.Color1 = System.Drawing.Color.Gray;
            this.lblInfoCreation.StateCommon.ShortText.Font = new System.Drawing.Font("Segoe UI", 8F);
            this.lblInfoCreation.TabIndex = 0;
            this.lblInfoCreation.Values.Text = "";
            this.lblInfoCreation.Visible = false;
            // 
            // lblInfoModification
            // 
            this.lblInfoModification.Location = new System.Drawing.Point(10, 30);
            this.lblInfoModification.Name = "lblInfoModification";
            this.lblInfoModification.Size = new System.Drawing.Size(6, 2);
            this.lblInfoModification.StateCommon.ShortText.Color1 = System.Drawing.Color.Gray;
            this.lblInfoModification.StateCommon.ShortText.Font = new System.Drawing.Font("Segoe UI", 8F);
            this.lblInfoModification.TabIndex = 1;
            this.lblInfoModification.Values.Text = "";
            this.lblInfoModification.Visible = false;
            // 
            // panelBoutons
            // 
            this.panelBoutons.Controls.Add(this.btnAnnuler);
            this.panelBoutons.Controls.Add(this.btnEnregistrer);
            this.panelBoutons.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.panelBoutons.Location = new System.Drawing.Point(10, 440);
            this.panelBoutons.Name = "panelBoutons";
            this.panelBoutons.Size = new System.Drawing.Size(580, 50);
            this.panelBoutons.TabIndex = 2;
            // 
            // btnEnregistrer
            // 
            this.btnEnregistrer.Location = new System.Drawing.Point(360, 10);
            this.btnEnregistrer.Name = "btnEnregistrer";
            this.btnEnregistrer.Size = new System.Drawing.Size(100, 32);
            this.btnEnregistrer.StateCommon.Back.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(122)))), ((int)(((byte)(204)))));
            this.btnEnregistrer.StateCommon.Back.Color2 = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(122)))), ((int)(((byte)(204)))));
            this.btnEnregistrer.StateCommon.Content.ShortText.Color1 = System.Drawing.Color.White;
            this.btnEnregistrer.TabIndex = 0;
            this.btnEnregistrer.Values.Text = "💾 Enregistrer";
            this.btnEnregistrer.Click += new System.EventHandler(this.btnEnregistrer_Click);
            // 
            // btnAnnuler
            // 
            this.btnAnnuler.Location = new System.Drawing.Point(470, 10);
            this.btnAnnuler.Name = "btnAnnuler";
            this.btnAnnuler.Size = new System.Drawing.Size(100, 32);
            this.btnAnnuler.TabIndex = 1;
            this.btnAnnuler.Values.Text = "❌ Annuler";
            this.btnAnnuler.Click += new System.EventHandler(this.btnAnnuler_Click);
            // 
            // FRM_CATEGORIE_EDIT
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(600, 500);
            this.Controls.Add(this.panelMain);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
            this.KeyPreview = true;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "FRM_CATEGORIE_EDIT";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.Text = "Catégorie";
            this.Load += new System.EventHandler(this.FRM_CATEGORIE_EDIT_Load);
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.FRM_CATEGORIE_EDIT_FormClosing);
            this.KeyDown += new System.Windows.Forms.KeyEventHandler(this.FRM_CATEGORIE_EDIT_KeyDown);
            ((System.ComponentModel.ISupportInitialize)(this.panelMain)).EndInit();
            this.panelMain.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupBoxDetails.Panel)).EndInit();
            this.groupBoxDetails.Panel.ResumeLayout(false);
            this.groupBoxDetails.Panel.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupBoxDetails)).EndInit();
            this.groupBoxDetails.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.cmbCategorieParent)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelInfo)).EndInit();
            this.panelInfo.ResumeLayout(false);
            this.panelInfo.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.panelBoutons)).EndInit();
            this.panelBoutons.ResumeLayout(false);
            this.ResumeLayout(false);
        }

        #endregion

        private Krypton.Toolkit.KryptonPanel panelMain;
        private Krypton.Toolkit.KryptonGroupBox groupBoxDetails;
        private Krypton.Toolkit.KryptonLabel lblCode;
        private Krypton.Toolkit.KryptonTextBox txtCode;
        private Krypton.Toolkit.KryptonLabel lblNom;
        private Krypton.Toolkit.KryptonTextBox txtNom;
        private Krypton.Toolkit.KryptonLabel lblDescription;
        private Krypton.Toolkit.KryptonTextBox txtDescription;
        private Krypton.Toolkit.KryptonLabel lblCategorieParent;
        private Krypton.Toolkit.KryptonComboBox cmbCategorieParent;
        private Krypton.Toolkit.KryptonCheckBox chkActif;
        private Krypton.Toolkit.KryptonPanel panelInfo;
        private Krypton.Toolkit.KryptonLabel lblInfoCreation;
        private Krypton.Toolkit.KryptonLabel lblInfoModification;
        private Krypton.Toolkit.KryptonPanel panelBoutons;
        private Krypton.Toolkit.KryptonButton btnEnregistrer;
        private Krypton.Toolkit.KryptonButton btnAnnuler;
    }
}

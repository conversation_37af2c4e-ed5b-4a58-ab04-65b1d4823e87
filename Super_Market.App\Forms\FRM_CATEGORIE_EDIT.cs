using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using Krypton.Toolkit;
using Super_Market.Business.Services;
using Super_Market.Core.Models;

namespace Super_Market.App.Forms
{
    public partial class FRM_CATEGORIE_EDIT : KryptonForm
    {
        private readonly CategorieService _categorieService;
        private readonly Utilisateur _utilisateurConnecte;
        private readonly Categorie _categorieAModifier;
        private readonly bool _modeConsultation;
        private bool _estNouveau;

        public FRM_CATEGORIE_EDIT(Utilisateur utilisateur, Categorie categorie = null, bool modeConsultation = false)
        {
            InitializeComponent();
            _categorieService = new CategorieService();
            _utilisateurConnecte = utilisateur;
            _categorieAModifier = categorie;
            _modeConsultation = modeConsultation;
            _estNouveau = categorie == null;
            
            InitialiserFormulaire();
        }

        private void InitialiserFormulaire()
        {
            if (_modeConsultation)
            {
                this.Text = "Consultation Catégorie";
                btnEnregistrer.Visible = false;
                btnAnnuler.Text = "Fermer";
            }
            else if (_estNouveau)
            {
                this.Text = "Nouvelle Catégorie";
            }
            else
            {
                this.Text = "Modifier Catégorie";
            }

            this.Size = new System.Drawing.Size(600, 500);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;

            // Charger les catégories parentes
            ChargerCategoriesParentes();

            // Charger les données si modification/consultation
            if (!_estNouveau)
            {
                ChargerDonneesCategorie();
            }
            else
            {
                // Générer un code automatique pour une nouvelle catégorie
                GenererCodeAutomatique();
            }

            // Configurer les contrôles selon le mode
            ConfigurerControles();
        }

        private async void ChargerCategoriesParentes()
        {
            try
            {
                var categories = await _categorieService.GetCategoriesPrincipalesAsync();
                
                cmbCategorieParent.Items.Clear();
                cmbCategorieParent.Items.Add(new { Id = (int?)null, Nom = "Aucune (Catégorie principale)" });
                
                foreach (var categorie in categories.Where(c => c.EstActif))
                {
                    // Exclure la catégorie actuelle si on est en modification
                    if (_categorieAModifier == null || categorie.Id != _categorieAModifier.Id)
                    {
                        cmbCategorieParent.Items.Add(new { Id = (int?)categorie.Id, Nom = categorie.Nom });
                    }
                }
                
                cmbCategorieParent.DisplayMember = "Nom";
                cmbCategorieParent.ValueMember = "Id";
                cmbCategorieParent.SelectedIndex = 0;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors du chargement des catégories parentes: {ex.Message}",
                              "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ChargerDonneesCategorie()
        {
            if (_categorieAModifier != null)
            {
                txtCode.Text = _categorieAModifier.Code ?? "";
                txtNom.Text = _categorieAModifier.Nom ?? "";
                txtDescription.Text = _categorieAModifier.Description ?? "";
                chkActif.Checked = _categorieAModifier.EstActif;
                
                // Sélectionner la catégorie parente
                if (_categorieAModifier.CategorieParentId.HasValue)
                {
                    for (int i = 0; i < cmbCategorieParent.Items.Count; i++)
                    {
                        var item = (dynamic)cmbCategorieParent.Items[i];
                        if (item.Id == _categorieAModifier.CategorieParentId.Value)
                        {
                            cmbCategorieParent.SelectedIndex = i;
                            break;
                        }
                    }
                }
                else
                {
                    cmbCategorieParent.SelectedIndex = 0;
                }

                // Afficher les informations de création/modification
                if (_categorieAModifier.DateCreation != default(DateTime))
                {
                    lblInfoCreation.Text = $"Créé le {_categorieAModifier.DateCreation:dd/MM/yyyy HH:mm} par {_categorieAModifier.UtilisateurCreation}";
                    lblInfoCreation.Visible = true;
                }

                if (_categorieAModifier.DateModification.HasValue)
                {
                    lblInfoModification.Text = $"Modifié le {_categorieAModifier.DateModification:dd/MM/yyyy HH:mm} par {_categorieAModifier.UtilisateurModification}";
                    lblInfoModification.Visible = true;
                }
            }
        }

        private async void GenererCodeAutomatique()
        {
            try
            {
                txtCode.Text = await _categorieService.GenererCodeAutomatiqueAsync();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors de la génération du code: {ex.Message}",
                              "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtCode.Text = "CAT001";
            }
        }

        private void ConfigurerControles()
        {
            bool enEdition = !_modeConsultation;
            
            txtCode.ReadOnly = !enEdition;
            txtNom.ReadOnly = !enEdition;
            txtDescription.ReadOnly = !enEdition;
            chkActif.Enabled = enEdition;
            cmbCategorieParent.Enabled = enEdition;
            
            if (enEdition)
            {
                txtNom.Focus();
            }
        }

        private async void btnEnregistrer_Click(object sender, EventArgs e)
        {
            await EnregistrerCategorie();
        }

        private async Task EnregistrerCategorie()
        {
            try
            {
                // Validation
                if (string.IsNullOrWhiteSpace(txtNom.Text))
                {
                    MessageBox.Show("Le nom de la catégorie est obligatoire.",
                                  "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtNom.Focus();
                    return;
                }

                if (txtNom.Text.Trim().Length < 2)
                {
                    MessageBox.Show("Le nom de la catégorie doit contenir au moins 2 caractères.",
                                  "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtNom.Focus();
                    return;
                }

                // Désactiver les contrôles pendant l'enregistrement
                btnEnregistrer.Enabled = false;
                btnAnnuler.Enabled = false;
                this.Cursor = Cursors.WaitCursor;

                var categorie = _categorieAModifier ?? new Categorie();
                
                categorie.Code = txtCode.Text.Trim();
                categorie.Nom = txtNom.Text.Trim();
                categorie.Description = txtDescription.Text.Trim();
                categorie.EstActif = chkActif.Checked;
                
                // Catégorie parente
                var parentItem = (dynamic)cmbCategorieParent.SelectedItem;
                categorie.CategorieParentId = parentItem?.Id;
                
                if (_estNouveau)
                {
                    // Nouveau
                    categorie.DateCreation = DateTime.Now;
                    categorie.UtilisateurCreation = _utilisateurConnecte.NomUtilisateur;
                    
                    await _categorieService.AjouterCategorieAsync(categorie);
                    
                    MessageBox.Show("Catégorie ajoutée avec succès !",
                                  "Succès", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    // Modification
                    categorie.DateModification = DateTime.Now;
                    categorie.UtilisateurModification = _utilisateurConnecte.NomUtilisateur;
                    
                    await _categorieService.ModifierCategorieAsync(categorie);
                    
                    MessageBox.Show("Catégorie modifiée avec succès !",
                                  "Succès", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }

                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors de l'enregistrement: {ex.Message}",
                              "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                // Réactiver les contrôles
                btnEnregistrer.Enabled = true;
                btnAnnuler.Enabled = true;
                this.Cursor = Cursors.Default;
            }
        }

        private void btnAnnuler_Click(object sender, EventArgs e)
        {
            if (_modeConsultation)
            {
                this.DialogResult = DialogResult.Cancel;
                this.Close();
                return;
            }

            // Vérifier s'il y a des modifications non sauvegardées
            if (YaDesModifications())
            {
                var result = MessageBox.Show(
                    "Des modifications ont été apportées. Voulez-vous les abandonner ?",
                    "Modifications non sauvegardées",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result == DialogResult.No)
                {
                    return;
                }
            }

            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private bool YaDesModifications()
        {
            if (_estNouveau)
            {
                return !string.IsNullOrWhiteSpace(txtNom.Text) ||
                       !string.IsNullOrWhiteSpace(txtDescription.Text) ||
                       cmbCategorieParent.SelectedIndex != 0;
            }
            else if (_categorieAModifier != null)
            {
                return txtCode.Text.Trim() != (_categorieAModifier.Code ?? "") ||
                       txtNom.Text.Trim() != (_categorieAModifier.Nom ?? "") ||
                       txtDescription.Text.Trim() != (_categorieAModifier.Description ?? "") ||
                       chkActif.Checked != _categorieAModifier.EstActif ||
                       GetCategorieParentSelectionnee() != _categorieAModifier.CategorieParentId;
            }

            return false;
        }

        private int? GetCategorieParentSelectionnee()
        {
            var item = (dynamic)cmbCategorieParent.SelectedItem;
            return item?.Id;
        }

        private void FRM_CATEGORIE_EDIT_Load(object sender, EventArgs e)
        {
            if (!_modeConsultation && _estNouveau)
            {
                txtNom.Focus();
            }
        }

        private void FRM_CATEGORIE_EDIT_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (!_modeConsultation && e.CloseReason == CloseReason.UserClosing)
            {
                if (YaDesModifications())
                {
                    var result = MessageBox.Show(
                        "Des modifications ont été apportées. Voulez-vous les abandonner ?",
                        "Modifications non sauvegardées",
                        MessageBoxButtons.YesNo,
                        MessageBoxIcon.Question);

                    if (result == DialogResult.No)
                    {
                        e.Cancel = true;
                    }
                }
            }
        }

        private void txtNom_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter && !_modeConsultation)
            {
                e.Handled = true;
                txtDescription.Focus();
            }
        }

        private void txtDescription_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter && !_modeConsultation)
            {
                e.Handled = true;
                cmbCategorieParent.Focus();
            }
        }

        private void FRM_CATEGORIE_EDIT_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Escape)
            {
                btnAnnuler_Click(sender, e);
            }
            else if (e.KeyCode == Keys.F2 && !_modeConsultation)
            {
                btnEnregistrer_Click(sender, e);
            }
        }
    }
}

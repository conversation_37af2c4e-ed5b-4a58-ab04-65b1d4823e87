namespace Super_Market.App.Forms
{
    partial class FRM_CLIENTS
    {
        private System.ComponentModel.IContainer components = null;

        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        private void InitializeComponent()
        {
            this.panelMain = new Krypton.Toolkit.KryptonPanel();
            this.splitContainer = new System.Windows.Forms.SplitContainer();
            this.dgvClients = new Krypton.Toolkit.KryptonDataGridView();
            this.panelDetails = new Krypton.Toolkit.KryptonPanel();
            this.groupBoxDetails = new Krypton.Toolkit.KryptonGroupBox();
            this.chkEstEntreprise = new Krypton.Toolkit.KryptonCheckBox();
            this.lblRaisonSociale = new Krypton.Toolkit.KryptonLabel();
            this.txtRaisonSociale = new Krypton.Toolkit.KryptonTextBox();
            this.lblNumeroRC = new Krypton.Toolkit.KryptonLabel();
            this.txtNumeroRC = new Krypton.Toolkit.KryptonTextBox();
            this.lblNumeroNIF = new Krypton.Toolkit.KryptonLabel();
            this.txtNumeroNIF = new Krypton.Toolkit.KryptonTextBox();
            this.lblNom = new Krypton.Toolkit.KryptonLabel();
            this.txtNom = new Krypton.Toolkit.KryptonTextBox();
            this.lblPrenom = new Krypton.Toolkit.KryptonLabel();
            this.txtPrenom = new Krypton.Toolkit.KryptonTextBox();
            this.lblEmail = new Krypton.Toolkit.KryptonLabel();
            this.txtEmail = new Krypton.Toolkit.KryptonTextBox();
            this.lblTelephone = new Krypton.Toolkit.KryptonLabel();
            this.txtTelephone = new Krypton.Toolkit.KryptonTextBox();
            this.lblTelephoneSecondaire = new Krypton.Toolkit.KryptonLabel();
            this.txtTelephoneSecondaire = new Krypton.Toolkit.KryptonTextBox();
            this.lblAdresse = new Krypton.Toolkit.KryptonLabel();
            this.txtAdresse = new Krypton.Toolkit.KryptonTextBox();
            this.lblVille = new Krypton.Toolkit.KryptonLabel();
            this.txtVille = new Krypton.Toolkit.KryptonTextBox();
            this.lblCodePostal = new Krypton.Toolkit.KryptonLabel();
            this.txtCodePostal = new Krypton.Toolkit.KryptonTextBox();
            this.lblPays = new Krypton.Toolkit.KryptonLabel();
            this.txtPays = new Krypton.Toolkit.KryptonTextBox();
            this.lblDateNaissance = new Krypton.Toolkit.KryptonLabel();
            this.dtpDateNaissance = new Krypton.Toolkit.KryptonDateTimePicker();
            this.lblLimiteCredit = new Krypton.Toolkit.KryptonLabel();
            this.numLimiteCredit = new Krypton.Toolkit.KryptonNumericUpDown();
            this.lblSoldeCompte = new Krypton.Toolkit.KryptonLabel();
            this.numSoldeCompte = new Krypton.Toolkit.KryptonNumericUpDown();
            this.lblPointsFidelite = new Krypton.Toolkit.KryptonLabel();
            this.numPointsFidelite = new Krypton.Toolkit.KryptonNumericUpDown();
            this.lblNotes = new Krypton.Toolkit.KryptonLabel();
            this.txtNotes = new Krypton.Toolkit.KryptonTextBox();
            this.chkActif = new Krypton.Toolkit.KryptonCheckBox();
            this.panelTop = new Krypton.Toolkit.KryptonPanel();
            this.lblTitre = new Krypton.Toolkit.KryptonLabel();
            this.panelRecherche = new Krypton.Toolkit.KryptonPanel();
            this.lblRecherche = new Krypton.Toolkit.KryptonLabel();
            this.txtRecherche = new Krypton.Toolkit.KryptonTextBox();
            this.btnActualiser = new Krypton.Toolkit.KryptonButton();
            this.panelBoutons = new Krypton.Toolkit.KryptonPanel();
            this.btnAjouter = new Krypton.Toolkit.KryptonButton();
            this.btnModifier = new Krypton.Toolkit.KryptonButton();
            this.btnSupprimer = new Krypton.Toolkit.KryptonButton();
            this.btnEnregistrer = new Krypton.Toolkit.KryptonButton();
            this.btnAnnuler = new Krypton.Toolkit.KryptonButton();
            this.btnFermer = new Krypton.Toolkit.KryptonButton();
            this.statusStrip = new System.Windows.Forms.StatusStrip();
            this.lblStatus = new System.Windows.Forms.ToolStripStatusLabel();
            this.progressBar = new System.Windows.Forms.ToolStripProgressBar();
            ((System.ComponentModel.ISupportInitialize)(this.panelMain)).BeginInit();
            this.panelMain.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainer)).BeginInit();
            this.splitContainer.Panel1.SuspendLayout();
            this.splitContainer.Panel2.SuspendLayout();
            this.splitContainer.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgvClients)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelDetails)).BeginInit();
            this.panelDetails.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupBoxDetails)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupBoxDetails.Panel)).BeginInit();
            this.groupBoxDetails.Panel.SuspendLayout();
            this.groupBoxDetails.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.panelTop)).BeginInit();
            this.panelTop.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.panelRecherche)).BeginInit();
            this.panelRecherche.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.panelBoutons)).BeginInit();
            this.panelBoutons.SuspendLayout();
            this.statusStrip.SuspendLayout();
            this.SuspendLayout();
            //
            // panelMain
            //
            this.panelMain.Controls.Add(this.splitContainer);
            this.panelMain.Controls.Add(this.panelRecherche);
            this.panelMain.Controls.Add(this.panelTop);
            this.panelMain.Controls.Add(this.panelBoutons);
            this.panelMain.Controls.Add(this.statusStrip);
            this.panelMain.Dock = System.Windows.Forms.DockStyle.Fill;
            this.panelMain.Location = new System.Drawing.Point(0, 0);
            this.panelMain.Name = "panelMain";
            this.panelMain.Size = new System.Drawing.Size(1200, 700);
            this.panelMain.TabIndex = 0;
            //
            // splitContainer
            //
            this.splitContainer.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainer.Location = new System.Drawing.Point(0, 120);
            this.splitContainer.Name = "splitContainer";
            //
            // splitContainer.Panel1
            //
            this.splitContainer.Panel1.Controls.Add(this.dgvClients);
            //
            // splitContainer.Panel2
            //
            this.splitContainer.Panel2.Controls.Add(this.panelDetails);
            this.splitContainer.Size = new System.Drawing.Size(1200, 510);
            this.splitContainer.SplitterDistance = 600;
            this.splitContainer.TabIndex = 0;
            //
            // dgvClients
            //
            this.dgvClients.AllowUserToAddRows = false;
            this.dgvClients.AllowUserToDeleteRows = false;
            this.dgvClients.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dgvClients.Location = new System.Drawing.Point(0, 0);
            this.dgvClients.Name = "dgvClients";
            this.dgvClients.ReadOnly = true;
            this.dgvClients.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.dgvClients.Size = new System.Drawing.Size(600, 510);
            this.dgvClients.TabIndex = 0;
            this.dgvClients.SelectionChanged += new System.EventHandler(this.dgvClients_SelectionChanged);
            //
            // panelDetails
            //
            this.panelDetails.Controls.Add(this.groupBoxDetails);
            this.panelDetails.Dock = System.Windows.Forms.DockStyle.Fill;
            this.panelDetails.Location = new System.Drawing.Point(0, 0);
            this.panelDetails.Name = "panelDetails";
            this.panelDetails.Padding = new System.Windows.Forms.Padding(5);
            this.panelDetails.Size = new System.Drawing.Size(596, 510);
            this.panelDetails.TabIndex = 0;
            //
            // groupBoxDetails
            //
            this.groupBoxDetails.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupBoxDetails.Location = new System.Drawing.Point(5, 5);
            this.groupBoxDetails.Name = "groupBoxDetails";
            //
            // groupBoxDetails.Panel
            //
            this.groupBoxDetails.Panel.AutoScroll = true;
            this.groupBoxDetails.Panel.Controls.Add(this.chkActif);
            this.groupBoxDetails.Panel.Controls.Add(this.txtNotes);
            this.groupBoxDetails.Panel.Controls.Add(this.lblNotes);
            this.groupBoxDetails.Panel.Controls.Add(this.numPointsFidelite);
            this.groupBoxDetails.Panel.Controls.Add(this.lblPointsFidelite);
            this.groupBoxDetails.Panel.Controls.Add(this.numSoldeCompte);
            this.groupBoxDetails.Panel.Controls.Add(this.lblSoldeCompte);
            this.groupBoxDetails.Panel.Controls.Add(this.numLimiteCredit);
            this.groupBoxDetails.Panel.Controls.Add(this.lblLimiteCredit);
            this.groupBoxDetails.Panel.Controls.Add(this.dtpDateNaissance);
            this.groupBoxDetails.Panel.Controls.Add(this.lblDateNaissance);
            this.groupBoxDetails.Panel.Controls.Add(this.txtPays);
            this.groupBoxDetails.Panel.Controls.Add(this.lblPays);
            this.groupBoxDetails.Panel.Controls.Add(this.txtCodePostal);
            this.groupBoxDetails.Panel.Controls.Add(this.lblCodePostal);
            this.groupBoxDetails.Panel.Controls.Add(this.txtVille);
            this.groupBoxDetails.Panel.Controls.Add(this.lblVille);
            this.groupBoxDetails.Panel.Controls.Add(this.txtAdresse);
            this.groupBoxDetails.Panel.Controls.Add(this.lblAdresse);
            this.groupBoxDetails.Panel.Controls.Add(this.txtTelephoneSecondaire);
            this.groupBoxDetails.Panel.Controls.Add(this.lblTelephoneSecondaire);
            this.groupBoxDetails.Panel.Controls.Add(this.txtTelephone);
            this.groupBoxDetails.Panel.Controls.Add(this.lblTelephone);
            this.groupBoxDetails.Panel.Controls.Add(this.txtEmail);
            this.groupBoxDetails.Panel.Controls.Add(this.lblEmail);
            this.groupBoxDetails.Panel.Controls.Add(this.txtPrenom);
            this.groupBoxDetails.Panel.Controls.Add(this.lblPrenom);
            this.groupBoxDetails.Panel.Controls.Add(this.txtNom);
            this.groupBoxDetails.Panel.Controls.Add(this.lblNom);
            this.groupBoxDetails.Panel.Controls.Add(this.txtNumeroNIF);
            this.groupBoxDetails.Panel.Controls.Add(this.lblNumeroNIF);
            this.groupBoxDetails.Panel.Controls.Add(this.txtNumeroRC);
            this.groupBoxDetails.Panel.Controls.Add(this.lblNumeroRC);
            this.groupBoxDetails.Panel.Controls.Add(this.txtRaisonSociale);
            this.groupBoxDetails.Panel.Controls.Add(this.lblRaisonSociale);
            this.groupBoxDetails.Panel.Controls.Add(this.chkEstEntreprise);
            this.groupBoxDetails.Size = new System.Drawing.Size(586, 500);
            this.groupBoxDetails.TabIndex = 0;
            this.groupBoxDetails.Values.Heading = "Détails du Client";
            //
            // FRM_CLIENTS
            //
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1200, 700);
            this.Controls.Add(this.panelMain);
            this.KeyPreview = true;
            this.Name = "FRM_CLIENTS";
            this.Text = "Gestion des Clients";
            this.Load += new System.EventHandler(this.FRM_CLIENTS_Load);
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.FRM_CLIENTS_FormClosing);
            ((System.ComponentModel.ISupportInitialize)(this.panelMain)).EndInit();
            this.panelMain.ResumeLayout(false);
            this.panelMain.PerformLayout();
            this.splitContainer.Panel1.ResumeLayout(false);
            this.splitContainer.Panel2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainer)).EndInit();
            this.splitContainer.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.dgvClients)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelDetails)).EndInit();
            this.panelDetails.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupBoxDetails.Panel)).EndInit();
            this.groupBoxDetails.Panel.ResumeLayout(false);
            this.groupBoxDetails.Panel.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupBoxDetails)).EndInit();
            this.groupBoxDetails.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.panelTop)).EndInit();
            this.panelTop.ResumeLayout(false);
            this.panelTop.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.panelRecherche)).EndInit();
            this.panelRecherche.ResumeLayout(false);
            this.panelRecherche.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.panelBoutons)).EndInit();
            this.panelBoutons.ResumeLayout(false);
            this.statusStrip.ResumeLayout(false);
            this.statusStrip.PerformLayout();
            this.ResumeLayout(false);
        }

        #endregion

        // Déclaration des contrôles (version simplifiée)
        private Krypton.Toolkit.KryptonPanel panelMain;
        private System.Windows.Forms.SplitContainer splitContainer;
        private Krypton.Toolkit.KryptonDataGridView dgvClients;
        private Krypton.Toolkit.KryptonPanel panelDetails;
        private Krypton.Toolkit.KryptonGroupBox groupBoxDetails;
        private Krypton.Toolkit.KryptonCheckBox chkEstEntreprise;
        private Krypton.Toolkit.KryptonLabel lblRaisonSociale;
        private Krypton.Toolkit.KryptonTextBox txtRaisonSociale;
        private Krypton.Toolkit.KryptonLabel lblNumeroRC;
        private Krypton.Toolkit.KryptonTextBox txtNumeroRC;
        private Krypton.Toolkit.KryptonLabel lblNumeroNIF;
        private Krypton.Toolkit.KryptonTextBox txtNumeroNIF;
        private Krypton.Toolkit.KryptonLabel lblNom;
        private Krypton.Toolkit.KryptonTextBox txtNom;
        private Krypton.Toolkit.KryptonLabel lblPrenom;
        private Krypton.Toolkit.KryptonTextBox txtPrenom;
        private Krypton.Toolkit.KryptonLabel lblEmail;
        private Krypton.Toolkit.KryptonTextBox txtEmail;
        private Krypton.Toolkit.KryptonLabel lblTelephone;
        private Krypton.Toolkit.KryptonTextBox txtTelephone;
        private Krypton.Toolkit.KryptonLabel lblTelephoneSecondaire;
        private Krypton.Toolkit.KryptonTextBox txtTelephoneSecondaire;
        private Krypton.Toolkit.KryptonLabel lblAdresse;
        private Krypton.Toolkit.KryptonTextBox txtAdresse;
        private Krypton.Toolkit.KryptonLabel lblVille;
        private Krypton.Toolkit.KryptonTextBox txtVille;
        private Krypton.Toolkit.KryptonLabel lblCodePostal;
        private Krypton.Toolkit.KryptonTextBox txtCodePostal;
        private Krypton.Toolkit.KryptonLabel lblPays;
        private Krypton.Toolkit.KryptonTextBox txtPays;
        private Krypton.Toolkit.KryptonLabel lblDateNaissance;
        private Krypton.Toolkit.KryptonDateTimePicker dtpDateNaissance;
        private Krypton.Toolkit.KryptonLabel lblLimiteCredit;
        private Krypton.Toolkit.KryptonNumericUpDown numLimiteCredit;
        private Krypton.Toolkit.KryptonLabel lblSoldeCompte;
        private Krypton.Toolkit.KryptonNumericUpDown numSoldeCompte;
        private Krypton.Toolkit.KryptonLabel lblPointsFidelite;
        private Krypton.Toolkit.KryptonNumericUpDown numPointsFidelite;
        private Krypton.Toolkit.KryptonLabel lblNotes;
        private Krypton.Toolkit.KryptonTextBox txtNotes;
        private Krypton.Toolkit.KryptonCheckBox chkActif;
        private Krypton.Toolkit.KryptonPanel panelTop;
        private Krypton.Toolkit.KryptonLabel lblTitre;
        private Krypton.Toolkit.KryptonPanel panelRecherche;
        private Krypton.Toolkit.KryptonLabel lblRecherche;
        private Krypton.Toolkit.KryptonTextBox txtRecherche;
        private Krypton.Toolkit.KryptonButton btnActualiser;
        private Krypton.Toolkit.KryptonPanel panelBoutons;
        private Krypton.Toolkit.KryptonButton btnAjouter;
        private Krypton.Toolkit.KryptonButton btnModifier;
        private Krypton.Toolkit.KryptonButton btnSupprimer;
        private Krypton.Toolkit.KryptonButton btnEnregistrer;
        private Krypton.Toolkit.KryptonButton btnAnnuler;
        private Krypton.Toolkit.KryptonButton btnFermer;
        private System.Windows.Forms.StatusStrip statusStrip;
        private System.Windows.Forms.ToolStripStatusLabel lblStatus;
        private System.Windows.Forms.ToolStripProgressBar progressBar;
    }
}
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using Krypton.Toolkit;
using Super_Market.Business.Services;
using Super_Market.Core.Models;

namespace Super_Market.App.Forms
{
    public partial class FRM_CLIENTS : KryptonForm
    {
        private readonly ClientService _clientService;
        private readonly Utilisateur _utilisateurConnecte;
        private List<Client> _clients;
        private Client _clientSelectionne;
        private bool _modeEdition = false;

        public FRM_CLIENTS(Utilisateur utilisateur)
        {
            InitializeComponent();
            _clientService = new ClientService();
            _utilisateurConnecte = utilisateur;
            InitialiserFormulaire();
        }

        private void InitialiserFormulaire()
        {
            this.Text = "Gestion des Clients";
            this.WindowState = FormWindowState.Maximized;
            
            // Configurer la grille
            ConfigurerGrille();
            
            // Charger les données
            ChargerClients();
            
            // État initial des boutons
            ActiverBoutons(false);
            btnAjouter.Enabled = true;
            btnActualiser.Enabled = true;
        }

        private void ConfigurerGrille()
        {
            dgvClients.AutoGenerateColumns = false;
            dgvClients.AllowUserToAddRows = false;
            dgvClients.AllowUserToDeleteRows = false;
            dgvClients.ReadOnly = true;
            dgvClients.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvClients.MultiSelect = false;

            dgvClients.Columns.Clear();
            
            dgvClients.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Id",
                HeaderText = "ID",
                DataPropertyName = "Id",
                Width = 60,
                Visible = false
            });

            dgvClients.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Type",
                HeaderText = "Type",
                DataPropertyName = "TypeClient",
                Width = 80
            });

            dgvClients.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Nom",
                HeaderText = "Nom/Raison Sociale",
                DataPropertyName = "NomComplet",
                Width = 200
            });

            dgvClients.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Email",
                HeaderText = "Email",
                DataPropertyName = "Email",
                Width = 180
            });

            dgvClients.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Telephone",
                HeaderText = "Téléphone",
                DataPropertyName = "Telephone",
                Width = 120
            });

            dgvClients.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Ville",
                HeaderText = "Ville",
                DataPropertyName = "Ville",
                Width = 100
            });

            dgvClients.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "SoldeCompte",
                HeaderText = "Solde",
                DataPropertyName = "SoldeCompte",
                Width = 100,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "F2" }
            });

            dgvClients.Columns.Add(new DataGridViewCheckBoxColumn
            {
                Name = "EstActif",
                HeaderText = "Actif",
                DataPropertyName = "EstActif",
                Width = 60
            });
        }

        private async void ChargerClients()
        {
            try
            {
                lblStatus.Text = "Chargement des clients...";
                progressBar.Visible = true;
                
                _clients = (await _clientService.GetAllClientsAsync()).ToList();
                
                // Enrichir les données pour l'affichage
                foreach (var client in _clients)
                {
                    // Propriétés dynamiques pour l'affichage
                    ((dynamic)client).TypeClient = client.EstEntreprise ? "Entreprise" : "Particulier";
                    ((dynamic)client).NomComplet = client.EstEntreprise ? 
                        client.RaisonSociale : 
                        $"{client.Nom} {client.Prenom}";
                }
                
                dgvClients.DataSource = _clients;
                
                lblStatus.Text = $"{_clients.Count} client(s) chargé(s)";
                progressBar.Visible = false;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors du chargement des clients: {ex.Message}",
                              "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                lblStatus.Text = "Erreur de chargement";
                progressBar.Visible = false;
            }
        }

        private void dgvClients_SelectionChanged(object sender, EventArgs e)
        {
            if (dgvClients.SelectedRows.Count > 0)
            {
                var index = dgvClients.SelectedRows[0].Index;
                if (index >= 0 && index < _clients.Count)
                {
                    _clientSelectionne = _clients[index];
                    AfficherClient(_clientSelectionne);
                    ActiverBoutons(true);
                }
            }
            else
            {
                _clientSelectionne = null;
                ViderChamps();
                ActiverBoutons(false);
            }
        }

        private void AfficherClient(Client client)
        {
            if (client != null)
            {
                chkEstEntreprise.Checked = client.EstEntreprise;
                
                if (client.EstEntreprise)
                {
                    txtRaisonSociale.Text = client.RaisonSociale ?? "";
                    txtNumeroRC.Text = client.NumeroRegistreCommerce ?? "";
                    txtNumeroNIF.Text = client.NumeroIdentificationFiscale ?? "";
                    txtNom.Text = "";
                    txtPrenom.Text = "";
                }
                else
                {
                    txtNom.Text = client.Nom ?? "";
                    txtPrenom.Text = client.Prenom ?? "";
                    txtRaisonSociale.Text = "";
                    txtNumeroRC.Text = "";
                    txtNumeroNIF.Text = "";
                }
                
                txtEmail.Text = client.Email ?? "";
                txtTelephone.Text = client.Telephone ?? "";
                txtTelephoneSecondaire.Text = client.TelephoneSecondaire ?? "";
                txtAdresse.Text = client.Adresse ?? "";
                txtVille.Text = client.Ville ?? "";
                txtCodePostal.Text = client.CodePostal ?? "";
                txtPays.Text = client.Pays ?? "Algérie";
                numLimiteCredit.Value = client.LimiteCredit;
                numSoldeCompte.Value = client.SoldeCompte;
                numPointsFidelite.Value = client.PointsFidelite;
                txtNotes.Text = client.Notes ?? "";
                chkActif.Checked = client.EstActif;
                
                if (client.DateNaissance.HasValue)
                    dtpDateNaissance.Value = client.DateNaissance.Value;
                else
                    dtpDateNaissance.Value = DateTime.Now.AddYears(-30);
            }
        }

        private void ViderChamps()
        {
            chkEstEntreprise.Checked = false;
            txtRaisonSociale.Text = "";
            txtNumeroRC.Text = "";
            txtNumeroNIF.Text = "";
            txtNom.Text = "";
            txtPrenom.Text = "";
            txtEmail.Text = "";
            txtTelephone.Text = "";
            txtTelephoneSecondaire.Text = "";
            txtAdresse.Text = "";
            txtVille.Text = "";
            txtCodePostal.Text = "";
            txtPays.Text = "Algérie";
            numLimiteCredit.Value = 0;
            numSoldeCompte.Value = 0;
            numPointsFidelite.Value = 0;
            txtNotes.Text = "";
            chkActif.Checked = true;
            dtpDateNaissance.Value = DateTime.Now.AddYears(-30);
        }

        private void ActiverBoutons(bool clientSelectionne)
        {
            btnModifier.Enabled = clientSelectionne && !_modeEdition;
            btnSupprimer.Enabled = clientSelectionne && !_modeEdition;
            btnEnregistrer.Enabled = _modeEdition;
            btnAnnuler.Enabled = _modeEdition;
        }

        private void ActiverModeEdition(bool activer)
        {
            _modeEdition = activer;
            
            chkEstEntreprise.Enabled = activer;
            txtRaisonSociale.ReadOnly = !activer;
            txtNumeroRC.ReadOnly = !activer;
            txtNumeroNIF.ReadOnly = !activer;
            txtNom.ReadOnly = !activer;
            txtPrenom.ReadOnly = !activer;
            txtEmail.ReadOnly = !activer;
            txtTelephone.ReadOnly = !activer;
            txtTelephoneSecondaire.ReadOnly = !activer;
            txtAdresse.ReadOnly = !activer;
            txtVille.ReadOnly = !activer;
            txtCodePostal.ReadOnly = !activer;
            txtPays.ReadOnly = !activer;
            numLimiteCredit.Enabled = activer;
            numSoldeCompte.Enabled = activer;
            numPointsFidelite.Enabled = activer;
            txtNotes.ReadOnly = !activer;
            chkActif.Enabled = activer;
            dtpDateNaissance.Enabled = activer;
            
            dgvClients.Enabled = !activer;
            
            ActiverBoutons(_clientSelectionne != null);
        }

        private void chkEstEntreprise_CheckedChanged(object sender, EventArgs e)
        {
            if (_modeEdition)
            {
                ConfigurerChampsSelonType();
            }
        }

        private void ConfigurerChampsSelonType()
        {
            bool estEntreprise = chkEstEntreprise.Checked;
            
            // Champs entreprise
            lblRaisonSociale.Visible = estEntreprise;
            txtRaisonSociale.Visible = estEntreprise;
            lblNumeroRC.Visible = estEntreprise;
            txtNumeroRC.Visible = estEntreprise;
            lblNumeroNIF.Visible = estEntreprise;
            txtNumeroNIF.Visible = estEntreprise;
            
            // Champs particulier
            lblNom.Visible = !estEntreprise;
            txtNom.Visible = !estEntreprise;
            lblPrenom.Visible = !estEntreprise;
            txtPrenom.Visible = !estEntreprise;
            lblDateNaissance.Visible = !estEntreprise;
            dtpDateNaissance.Visible = !estEntreprise;
        }

        private async void btnAjouter_Click(object sender, EventArgs e)
        {
            _clientSelectionne = null;
            ViderChamps();
            ActiverModeEdition(true);
            ConfigurerChampsSelonType();
            
            if (chkEstEntreprise.Checked)
                txtRaisonSociale.Focus();
            else
                txtNom.Focus();
        }

        private void btnModifier_Click(object sender, EventArgs e)
        {
            if (_clientSelectionne != null)
            {
                ActiverModeEdition(true);
                ConfigurerChampsSelonType();
                
                if (_clientSelectionne.EstEntreprise)
                    txtRaisonSociale.Focus();
                else
                    txtNom.Focus();
            }
        }

        private async void btnEnregistrer_Click(object sender, EventArgs e)
        {
            await EnregistrerClient();
        }

        private async Task EnregistrerClient()
        {
            try
            {
                // Validation
                if (string.IsNullOrWhiteSpace(txtEmail.Text))
                {
                    MessageBox.Show("L'email est obligatoire.",
                                  "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtEmail.Focus();
                    return;
                }

                if (chkEstEntreprise.Checked)
                {
                    if (string.IsNullOrWhiteSpace(txtRaisonSociale.Text))
                    {
                        MessageBox.Show("La raison sociale est obligatoire pour une entreprise.",
                                      "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        txtRaisonSociale.Focus();
                        return;
                    }
                }
                else
                {
                    if (string.IsNullOrWhiteSpace(txtNom.Text) || string.IsNullOrWhiteSpace(txtPrenom.Text))
                    {
                        MessageBox.Show("Le nom et le prénom sont obligatoires pour un particulier.",
                                      "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        if (string.IsNullOrWhiteSpace(txtNom.Text))
                            txtNom.Focus();
                        else
                            txtPrenom.Focus();
                        return;
                    }
                }

                var client = _clientSelectionne ?? new Client();
                
                client.EstEntreprise = chkEstEntreprise.Checked;
                client.RaisonSociale = txtRaisonSociale.Text.Trim();
                client.NumeroRegistreCommerce = txtNumeroRC.Text.Trim();
                client.NumeroIdentificationFiscale = txtNumeroNIF.Text.Trim();
                client.Nom = txtNom.Text.Trim();
                client.Prenom = txtPrenom.Text.Trim();
                client.Email = txtEmail.Text.Trim();
                client.Telephone = txtTelephone.Text.Trim();
                client.TelephoneSecondaire = txtTelephoneSecondaire.Text.Trim();
                client.Adresse = txtAdresse.Text.Trim();
                client.Ville = txtVille.Text.Trim();
                client.CodePostal = txtCodePostal.Text.Trim();
                client.Pays = txtPays.Text.Trim();
                client.LimiteCredit = numLimiteCredit.Value;
                client.SoldeCompte = numSoldeCompte.Value;
                client.PointsFidelite = (int)numPointsFidelite.Value;
                client.Notes = txtNotes.Text.Trim();
                client.EstActif = chkActif.Checked;
                
                if (!client.EstEntreprise)
                {
                    client.DateNaissance = dtpDateNaissance.Value;
                }
                
                if (_clientSelectionne == null)
                {
                    // Nouveau
                    client.DateCreation = DateTime.Now;
                    client.UtilisateurCreation = _utilisateurConnecte.NomUtilisateur;
                    
                    await _clientService.AjouterClientAsync(client);
                    MessageBox.Show("Client ajouté avec succès !",
                                  "Succès", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    // Modification
                    client.DateModification = DateTime.Now;
                    client.UtilisateurModification = _utilisateurConnecte.NomUtilisateur;
                    
                    await _clientService.ModifierClientAsync(client);
                    MessageBox.Show("Client modifié avec succès !",
                                  "Succès", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }

                ActiverModeEdition(false);
                ChargerClients();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors de l'enregistrement: {ex.Message}",
                              "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnAnnuler_Click(object sender, EventArgs e)
        {
            ActiverModeEdition(false);
            if (_clientSelectionne != null)
            {
                AfficherClient(_clientSelectionne);
            }
            else
            {
                ViderChamps();
            }
            ConfigurerChampsSelonType();
        }

        private async void btnSupprimer_Click(object sender, EventArgs e)
        {
            if (_clientSelectionne != null)
            {
                var nomClient = _clientSelectionne.EstEntreprise ? 
                    _clientSelectionne.RaisonSociale : 
                    $"{_clientSelectionne.Nom} {_clientSelectionne.Prenom}";
                    
                var result = MessageBox.Show(
                    $"Êtes-vous sûr de vouloir supprimer le client '{nomClient}' ?",
                    "Confirmation de suppression",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    try
                    {
                        await _clientService.SupprimerClientAsync(_clientSelectionne.Id);
                        MessageBox.Show("Client supprimé avec succès !",
                                      "Succès", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        ChargerClients();
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"Erreur lors de la suppression: {ex.Message}",
                                      "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
        }

        private void btnActualiser_Click(object sender, EventArgs e)
        {
            ChargerClients();
        }

        private void btnFermer_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void txtRecherche_TextChanged(object sender, EventArgs e)
        {
            FiltrerClients();
        }

        private void FiltrerClients()
        {
            if (_clients == null) return;

            var termeRecherche = txtRecherche.Text.ToLower();
            
            if (string.IsNullOrWhiteSpace(termeRecherche))
            {
                dgvClients.DataSource = _clients;
            }
            else
            {
                var clientsFiltres = _clients.Where(c =>
                    (c.Nom?.ToLower().Contains(termeRecherche) ?? false) ||
                    (c.Prenom?.ToLower().Contains(termeRecherche) ?? false) ||
                    (c.RaisonSociale?.ToLower().Contains(termeRecherche) ?? false) ||
                    (c.Email?.ToLower().Contains(termeRecherche) ?? false) ||
                    (c.Telephone?.ToLower().Contains(termeRecherche) ?? false) ||
                    (c.Ville?.ToLower().Contains(termeRecherche) ?? false)
                ).ToList();
                
                dgvClients.DataSource = clientsFiltres;
            }
        }

        private void FRM_CLIENTS_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (_modeEdition)
            {
                var result = MessageBox.Show(
                    "Des modifications sont en cours. Voulez-vous les abandonner ?",
                    "Modifications en cours",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result == DialogResult.No)
                {
                    e.Cancel = true;
                }
            }
        }

        private void FRM_CLIENTS_Load(object sender, EventArgs e)
        {
            ConfigurerChampsSelonType();
        }
    }
}

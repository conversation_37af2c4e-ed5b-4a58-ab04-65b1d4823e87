using System;
using System.Threading.Tasks;
using System.Windows.Forms;
using Krypton.Toolkit;
using Super_Market.Business.Services;
using Super_Market.Core.Models;

namespace Super_Market.App.Forms
{
    public partial class FRM_LOGIN : KryptonForm
    {
        private readonly UtilisateurService _utilisateurService;
        
        public FRM_LOGIN()
        {
            InitializeComponent();
            _utilisateurService = new UtilisateurService();
            InitialiserFormulaire();
        }
        
        private void InitialiserFormulaire()
        {
            this.Text = "ZinStore - Connexion";
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            
            // Définir les valeurs par défaut pour les tests
            txtNomUtilisateur.Text = "admin";
            txtMotDePasse.Text = "admin123";
            
            // Focus sur le nom d'utilisateur
            txtNomUtilisateur.Focus();
        }
        
        private async void btnConnecter_Click(object sender, EventArgs e)
        {
            await TenterConnexion();
        }
        
        private async void txtMotDePasse_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Enter)
            {
                await TenterConnexion();
            }
        }
        
        private async Task TenterConnexion()
        {
            try
            {
                // Validation des champs
                if (string.IsNullOrWhiteSpace(txtNomUtilisateur.Text))
                {
                    MessageBox.Show("Veuillez saisir votre nom d'utilisateur.", 
                                  "Champ requis", 
                                  MessageBoxButtons.OK, 
                                  MessageBoxIcon.Warning);
                    txtNomUtilisateur.Focus();
                    return;
                }
                
                if (string.IsNullOrWhiteSpace(txtMotDePasse.Text))
                {
                    MessageBox.Show("Veuillez saisir votre mot de passe.", 
                                  "Champ requis", 
                                  MessageBoxButtons.OK, 
                                  MessageBoxIcon.Warning);
                    txtMotDePasse.Focus();
                    return;
                }
                
                // Désactiver les contrôles pendant la connexion
                btnConnecter.Enabled = false;
                txtNomUtilisateur.Enabled = false;
                txtMotDePasse.Enabled = false;
                lblStatut.Text = "Connexion en cours...";
                lblStatut.Visible = true;
                
                // Tentative d'authentification
                var utilisateur = await _utilisateurService.AuthentifierAsync(
                    txtNomUtilisateur.Text.Trim(), 
                    txtMotDePasse.Text);
                
                if (utilisateur != null)
                {
                    // Connexion réussie
                    lblStatut.Text = "Connexion réussie !";
                    
                    // Ouvrir le formulaire principal
                    this.Hide();
                    var formPrincipal = new FRM_PRINCIPAL(utilisateur);
                    formPrincipal.ShowDialog();
                    
                    // Fermer le formulaire de connexion
                    this.Close();
                }
                else
                {
                    // Échec de la connexion
                    MessageBox.Show("Nom d'utilisateur ou mot de passe incorrect.", 
                                  "Erreur de connexion", 
                                  MessageBoxButtons.OK, 
                                  MessageBoxIcon.Error);
                    
                    txtMotDePasse.Text = "";
                    txtNomUtilisateur.Focus();
                    lblStatut.Text = "Échec de la connexion";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors de la connexion: {ex.Message}", 
                              "Erreur", 
                              MessageBoxButtons.OK, 
                              MessageBoxIcon.Error);
                lblStatut.Text = "Erreur de connexion";
            }
            finally
            {
                // Réactiver les contrôles
                btnConnecter.Enabled = true;
                txtNomUtilisateur.Enabled = true;
                txtMotDePasse.Enabled = true;
            }
        }
        
        private void btnAnnuler_Click(object sender, EventArgs e)
        {
            var result = MessageBox.Show("Êtes-vous sûr de vouloir quitter l'application ?", 
                                       "Confirmation", 
                                       MessageBoxButtons.YesNo, 
                                       MessageBoxIcon.Question);
            
            if (result == DialogResult.Yes)
            {
                Application.Exit();
            }
        }
        
        private void btnAfficherMotDePasse_Click(object sender, EventArgs e)
        {
            if (txtMotDePasse.PasswordChar == '*')
            {
                txtMotDePasse.PasswordChar = '\0';
                btnAfficherMotDePasse.Text = "🙈";
            }
            else
            {
                txtMotDePasse.PasswordChar = '*';
                btnAfficherMotDePasse.Text = "👁";
            }
        }
        
        private void FRM_LOGIN_Load(object sender, EventArgs e)
        {
            // Vérifier la connexion à la base de données
            try
            {
                bool connectionOk = Super_Market.Data.DatabaseConfig.TestConnection();
                if (!connectionOk)
                {
                    MessageBox.Show("Impossible de se connecter à la base de données.\n" +
                                  "Veuillez vérifier la configuration de la base de données.", 
                                  "Erreur de base de données", 
                                  MessageBoxButtons.OK, 
                                  MessageBoxIcon.Error);
                }
                else
                {
                    lblStatut.Text = "Prêt pour la connexion";
                    lblStatut.ForeColor = System.Drawing.Color.Green;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors de la vérification de la base de données: {ex.Message}", 
                              "Erreur", 
                              MessageBoxButtons.OK, 
                              MessageBoxIcon.Error);
            }
        }
        
        private void lnkMotDePasseOublie_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            MessageBox.Show("Contactez l'administrateur système pour réinitialiser votre mot de passe.", 
                          "Mot de passe oublié", 
                          MessageBoxButtons.OK, 
                          MessageBoxIcon.Information);
        }
        
        private void txtNomUtilisateur_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Enter)
            {
                txtMotDePasse.Focus();
            }
        }
        
        private void FRM_LOGIN_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Escape)
            {
                btnAnnuler_Click(sender, e);
            }
        }
    }
}

using System;
using System.Windows.Forms;
using Krypton.Toolkit;
using Super_Market.Core.Models;

namespace Super_Market.App.Forms
{
    public partial class FRM_PRINCIPAL : KryptonForm
    {
        private Utilisateur _utilisateurConnecte;
        
        public FRM_PRINCIPAL(Utilisateur utilisateur)
        {
            InitializeComponent();
            _utilisateurConnecte = utilisateur;
            InitialiserFormulaire();
        }
        
        private void InitialiserFormulaire()
        {
            this.Text = $"ZinStore - Système de Gestion de Supermarché - Connecté: {_utilisateurConnecte.Prenom} {_utilisateurConnecte.Nom}";
            this.WindowState = FormWindowState.Maximized;
            
            // Configurer les permissions selon le type d'utilisateur
            ConfigurerPermissions();
            
            // Afficher les informations de l'utilisateur
            lblUtilisateurConnecte.Text = $"Bienvenue, {_utilisateurConnecte.Prenom} {_utilisateurConnecte.Nom}";
            lblTypeUtilisateur.Text = $"Type: {_utilisateurConnecte.TypeUtilisateur}";
            lblDerniereConnexion.Text = $"Dernière connexion: {_utilisateurConnecte.DerniereConnexion?.ToString("dd/MM/yyyy HH:mm")}";
        }
        
        private void ConfigurerPermissions()
        {
            // Masquer certains menus selon le type d'utilisateur
            switch (_utilisateurConnecte.TypeUtilisateur)
            {
                case Core.Enums.TypeUtilisateur.Caissier:
                    menuGestionProduits.Visible = false;
                    menuGestionFournisseurs.Visible = false;
                    menuComptabilite.Visible = false;
                    menuParametres.Visible = false;
                    menuUtilisateurs.Visible = false;
                    break;
                    
                case Core.Enums.TypeUtilisateur.Vendeur:
                    menuComptabilite.Visible = false;
                    menuParametres.Visible = false;
                    menuUtilisateurs.Visible = false;
                    break;
                    
                case Core.Enums.TypeUtilisateur.Magasinier:
                    menuVentes.Visible = false;
                    menuClients.Visible = false;
                    menuComptabilite.Visible = false;
                    menuParametres.Visible = false;
                    menuUtilisateurs.Visible = false;
                    break;
                    
                case Core.Enums.TypeUtilisateur.Gerant:
                    menuUtilisateurs.Visible = false;
                    break;
                    
                case Core.Enums.TypeUtilisateur.Administrateur:
                    // Accès complet
                    break;
            }
        }
        
        private void btnVentes_Click(object sender, EventArgs e)
        {
            OuvrirFormulaire(new FRM_VENTES(_utilisateurConnecte));
        }
        
        private void btnProduits_Click(object sender, EventArgs e)
        {
            OuvrirFormulaire(new FRM_PRODUITS_LIST(_utilisateurConnecte));
        }
        
        private void btnCategories_Click(object sender, EventArgs e)
        {
            OuvrirFormulaire(new FRM_CATEGORIES_LIST(_utilisateurConnecte));
        }
        
        private void btnClients_Click(object sender, EventArgs e)
        {
            OuvrirFormulaire(new FRM_CLIENTS(_utilisateurConnecte));
        }
        
        private void btnFournisseurs_Click(object sender, EventArgs e)
        {
            OuvrirFormulaire(new FRM_FOURNISSEURS(_utilisateurConnecte));
        }
        
        private void btnStock_Click(object sender, EventArgs e)
        {
            OuvrirFormulaire(new FRM_STOCK(_utilisateurConnecte));
        }
        
        private void btnAchats_Click(object sender, EventArgs e)
        {
            OuvrirFormulaire(new FRM_ACHATS(_utilisateurConnecte));
        }
        
        private void btnRapports_Click(object sender, EventArgs e)
        {
            OuvrirFormulaire(new FRM_RAPPORTS(_utilisateurConnecte));
        }
        
        private void btnParametres_Click(object sender, EventArgs e)
        {
            OuvrirFormulaire(new FRM_PARAMETRES(_utilisateurConnecte));
        }
        
        private void btnUtilisateurs_Click(object sender, EventArgs e)
        {
            OuvrirFormulaire(new FRM_UTILISATEURS(_utilisateurConnecte));
        }
        
        private void btnDeconnexion_Click(object sender, EventArgs e)
        {
            var result = MessageBox.Show("Êtes-vous sûr de vouloir vous déconnecter ?", 
                                       "Confirmation", 
                                       MessageBoxButtons.YesNo, 
                                       MessageBoxIcon.Question);
            
            if (result == DialogResult.Yes)
            {
                // Déconnecter l'utilisateur
                // TODO: Appeler le service pour mettre à jour le statut de connexion
                
                this.Hide();
                var loginForm = new FRM_LOGIN();
                loginForm.ShowDialog();
                this.Close();
            }
        }
        
        private void btnQuitter_Click(object sender, EventArgs e)
        {
            var result = MessageBox.Show("Êtes-vous sûr de vouloir quitter l'application ?", 
                                       "Confirmation", 
                                       MessageBoxButtons.YesNo, 
                                       MessageBoxIcon.Question);
            
            if (result == DialogResult.Yes)
            {
                Application.Exit();
            }
        }
        
        private void OuvrirFormulaire(Form formulaire)
        {
            try
            {
                formulaire.MdiParent = this;
                formulaire.Show();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors de l'ouverture du formulaire: {ex.Message}", 
                              "Erreur", 
                              MessageBoxButtons.OK, 
                              MessageBoxIcon.Error);
            }
        }
        
        private void timerHeure_Tick(object sender, EventArgs e)
        {
            lblHeure.Text = DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss");
        }
        
        private void FRM_PRINCIPAL_Load(object sender, EventArgs e)
        {
            timerHeure.Start();
            
            // Charger les statistiques du tableau de bord
            ChargerTableauDeBord();
        }
        
        private async void ChargerTableauDeBord()
        {
            try
            {
                // TODO: Implémenter le chargement des statistiques
                lblNombreProduits.Text = "Produits: 0";
                lblNombreClients.Text = "Clients: 0";
                lblVentesJour.Text = "Ventes du jour: 0,00 DZD";
                lblStockFaible.Text = "Alertes stock: 0";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors du chargement du tableau de bord: {ex.Message}", 
                              "Erreur", 
                              MessageBoxButtons.OK, 
                              MessageBoxIcon.Warning);
            }
        }
        
        private void FRM_PRINCIPAL_FormClosing(object sender, FormClosingEventArgs e)
        {
            // Déconnecter l'utilisateur lors de la fermeture
            // TODO: Appeler le service pour mettre à jour le statut de connexion
        }
    }
}

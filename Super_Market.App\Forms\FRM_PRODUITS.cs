using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using Krypton.Toolkit;
using Super_Market.Business.Services;
using Super_Market.Core.Models;

namespace Super_Market.App.Forms
{
    public partial class FRM_PRODUITS : KryptonForm
    {
        private readonly ProduitService _produitService;
        private readonly CategorieService _categorieService;
        private readonly Utilisateur _utilisateurConnecte;
        private List<Produit> _produits;
        private Produit _produitSelectionne;
        private bool _modeEdition = false;

        public FRM_PRODUITS(Utilisateur utilisateur)
        {
            InitializeComponent();
            _produitService = new ProduitService();
            _categorieService = new CategorieService();
            _utilisateurConnecte = utilisateur;
            InitialiserFormulaire();
        }

        private void InitialiserFormulaire()
        {
            this.Text = "Gestion des Produits";
            this.WindowState = FormWindowState.Maximized;
            
            // Configurer la grille
            ConfigurerGrille();
            
            // Charger les données
            ChargerProduits();
            ChargerCategories();
            
            // État initial des boutons
            ActiverBoutons(false);
            btnAjouter.Enabled = true;
            btnActualiser.Enabled = true;
        }

        private void ConfigurerGrille()
        {
            dgvProduits.AutoGenerateColumns = false;
            dgvProduits.AllowUserToAddRows = false;
            dgvProduits.AllowUserToDeleteRows = false;
            dgvProduits.ReadOnly = true;
            dgvProduits.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvProduits.MultiSelect = false;

            dgvProduits.Columns.Clear();
            
            dgvProduits.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Id",
                HeaderText = "ID",
                DataPropertyName = "Id",
                Width = 60,
                Visible = false
            });

            dgvProduits.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "CodeBarre",
                HeaderText = "Code-Barres",
                DataPropertyName = "CodeBarre",
                Width = 120
            });

            dgvProduits.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Nom",
                HeaderText = "Nom du Produit",
                DataPropertyName = "Nom",
                Width = 200
            });

            dgvProduits.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Categorie",
                HeaderText = "Catégorie",
                DataPropertyName = "CategorieNom",
                Width = 150
            });

            dgvProduits.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "PrixAchat",
                HeaderText = "Prix Achat",
                DataPropertyName = "PrixAchat",
                Width = 100,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "F2" }
            });

            dgvProduits.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "PrixVente",
                HeaderText = "Prix Vente",
                DataPropertyName = "PrixVente",
                Width = 100,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "F2" }
            });

            dgvProduits.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Unite",
                HeaderText = "Unité",
                DataPropertyName = "Unite",
                Width = 80
            });

            dgvProduits.Columns.Add(new DataGridViewCheckBoxColumn
            {
                Name = "EstActif",
                HeaderText = "Actif",
                DataPropertyName = "EstActif",
                Width = 60
            });
        }

        private async void ChargerProduits()
        {
            try
            {
                lblStatus.Text = "Chargement des produits...";
                progressBar.Visible = true;
                
                _produits = (await _produitService.GetAllProduitsAsync()).ToList();
                
                // Enrichir avec les noms des catégories
                var categories = await _categorieService.GetAllCategoriesAsync();
                foreach (var produit in _produits)
                {
                    var categorie = categories.FirstOrDefault(c => c.Id == produit.CategorieId);
                    if (categorie != null)
                    {
                        // Utiliser une propriété dynamique pour l'affichage
                        ((dynamic)produit).CategorieNom = categorie.Nom;
                    }
                }
                
                dgvProduits.DataSource = _produits;
                
                lblStatus.Text = $"{_produits.Count} produit(s) chargé(s)";
                progressBar.Visible = false;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors du chargement des produits: {ex.Message}",
                              "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                lblStatus.Text = "Erreur de chargement";
                progressBar.Visible = false;
            }
        }

        private async void ChargerCategories()
        {
            try
            {
                var categories = await _categorieService.GetAllCategoriesAsync();
                
                cmbCategorie.Items.Clear();
                foreach (var categorie in categories.Where(c => c.EstActif))
                {
                    cmbCategorie.Items.Add(new { Id = categorie.Id, Nom = categorie.Nom });
                }
                
                cmbCategorie.DisplayMember = "Nom";
                cmbCategorie.ValueMember = "Id";
                
                if (cmbCategorie.Items.Count > 0)
                    cmbCategorie.SelectedIndex = 0;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors du chargement des catégories: {ex.Message}",
                              "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void dgvProduits_SelectionChanged(object sender, EventArgs e)
        {
            if (dgvProduits.SelectedRows.Count > 0)
            {
                var index = dgvProduits.SelectedRows[0].Index;
                if (index >= 0 && index < _produits.Count)
                {
                    _produitSelectionne = _produits[index];
                    AfficherProduit(_produitSelectionne);
                    ActiverBoutons(true);
                }
            }
            else
            {
                _produitSelectionne = null;
                ViderChamps();
                ActiverBoutons(false);
            }
        }

        private void AfficherProduit(Produit produit)
        {
            if (produit != null)
            {
                txtCodeBarre.Text = produit.CodeBarre ?? "";
                txtCodeInterne.Text = produit.CodeInterne ?? "";
                txtNom.Text = produit.Nom ?? "";
                txtDescription.Text = produit.Description ?? "";
                txtMarque.Text = produit.Marque ?? "";
                txtUnite.Text = produit.Unite ?? "";
                numPrixAchat.Value = produit.PrixAchat;
                numPrixVente.Value = produit.PrixVente;
                numPrixPromotion.Value = produit.PrixPromotion ?? 0;
                numStockMinimum.Value = produit.StockMinimum;
                numStockMaximum.Value = produit.StockMaximum;
                numTVA.Value = produit.TVA ?? 19;
                chkActif.Checked = produit.EstActif;
                
                // Sélectionner la catégorie
                for (int i = 0; i < cmbCategorie.Items.Count; i++)
                {
                    var item = (dynamic)cmbCategorie.Items[i];
                    if (item.Id == produit.CategorieId)
                    {
                        cmbCategorie.SelectedIndex = i;
                        break;
                    }
                }
                
                // Dates de promotion
                if (produit.DateDebutPromotion.HasValue)
                    dtpDebutPromotion.Value = produit.DateDebutPromotion.Value;
                else
                    dtpDebutPromotion.Value = DateTime.Now;
                    
                if (produit.DateFinPromotion.HasValue)
                    dtpFinPromotion.Value = produit.DateFinPromotion.Value;
                else
                    dtpFinPromotion.Value = DateTime.Now.AddDays(30);
            }
        }

        private void ViderChamps()
        {
            txtCodeBarre.Text = "";
            txtCodeInterne.Text = "";
            txtNom.Text = "";
            txtDescription.Text = "";
            txtMarque.Text = "";
            txtUnite.Text = "Pièce";
            numPrixAchat.Value = 0;
            numPrixVente.Value = 0;
            numPrixPromotion.Value = 0;
            numStockMinimum.Value = 0;
            numStockMaximum.Value = 1000;
            numTVA.Value = 19;
            chkActif.Checked = true;
            dtpDebutPromotion.Value = DateTime.Now;
            dtpFinPromotion.Value = DateTime.Now.AddDays(30);
            
            if (cmbCategorie.Items.Count > 0)
                cmbCategorie.SelectedIndex = 0;
        }

        private void ActiverBoutons(bool produitSelectionne)
        {
            btnModifier.Enabled = produitSelectionne && !_modeEdition;
            btnSupprimer.Enabled = produitSelectionne && !_modeEdition;
            btnEnregistrer.Enabled = _modeEdition;
            btnAnnuler.Enabled = _modeEdition;
        }

        private void ActiverModeEdition(bool activer)
        {
            _modeEdition = activer;
            
            txtCodeBarre.ReadOnly = !activer;
            txtCodeInterne.ReadOnly = !activer;
            txtNom.ReadOnly = !activer;
            txtDescription.ReadOnly = !activer;
            txtMarque.ReadOnly = !activer;
            txtUnite.ReadOnly = !activer;
            numPrixAchat.Enabled = activer;
            numPrixVente.Enabled = activer;
            numPrixPromotion.Enabled = activer;
            numStockMinimum.Enabled = activer;
            numStockMaximum.Enabled = activer;
            numTVA.Enabled = activer;
            chkActif.Enabled = activer;
            cmbCategorie.Enabled = activer;
            dtpDebutPromotion.Enabled = activer;
            dtpFinPromotion.Enabled = activer;
            
            dgvProduits.Enabled = !activer;
            
            ActiverBoutons(_produitSelectionne != null);
        }

        private async void btnAjouter_Click(object sender, EventArgs e)
        {
            _produitSelectionne = null;
            ViderChamps();
            ActiverModeEdition(true);
            txtCodeBarre.Focus();
        }

        private void btnModifier_Click(object sender, EventArgs e)
        {
            if (_produitSelectionne != null)
            {
                ActiverModeEdition(true);
                txtNom.Focus();
            }
        }

        private async void btnEnregistrer_Click(object sender, EventArgs e)
        {
            await EnregistrerProduit();
        }

        private async Task EnregistrerProduit()
        {
            try
            {
                // Validation
                if (string.IsNullOrWhiteSpace(txtNom.Text))
                {
                    MessageBox.Show("Le nom du produit est obligatoire.",
                                  "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtNom.Focus();
                    return;
                }

                if (string.IsNullOrWhiteSpace(txtCodeBarre.Text))
                {
                    MessageBox.Show("Le code-barres est obligatoire.",
                                  "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtCodeBarre.Focus();
                    return;
                }

                if (numPrixVente.Value <= numPrixAchat.Value)
                {
                    MessageBox.Show("Le prix de vente doit être supérieur au prix d'achat.",
                                  "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    numPrixVente.Focus();
                    return;
                }

                var produit = _produitSelectionne ?? new Produit();
                
                produit.CodeBarre = txtCodeBarre.Text.Trim();
                produit.CodeInterne = txtCodeInterne.Text.Trim();
                produit.Nom = txtNom.Text.Trim();
                produit.Description = txtDescription.Text.Trim();
                produit.Marque = txtMarque.Text.Trim();
                produit.Unite = txtUnite.Text.Trim();
                produit.PrixAchat = numPrixAchat.Value;
                produit.PrixVente = numPrixVente.Value;
                produit.PrixPromotion = numPrixPromotion.Value > 0 ? numPrixPromotion.Value : (decimal?)null;
                produit.StockMinimum = (int)numStockMinimum.Value;
                produit.StockMaximum = (int)numStockMaximum.Value;
                produit.TVA = numTVA.Value;
                produit.EstActif = chkActif.Checked;
                
                // Catégorie
                var categorieItem = (dynamic)cmbCategorie.SelectedItem;
                produit.CategorieId = categorieItem.Id;
                
                // Dates de promotion
                if (produit.PrixPromotion.HasValue)
                {
                    produit.DateDebutPromotion = dtpDebutPromotion.Value;
                    produit.DateFinPromotion = dtpFinPromotion.Value;
                }
                else
                {
                    produit.DateDebutPromotion = null;
                    produit.DateFinPromotion = null;
                }
                
                if (_produitSelectionne == null)
                {
                    // Nouveau
                    produit.DateCreation = DateTime.Now;
                    produit.UtilisateurCreation = _utilisateurConnecte.NomUtilisateur;
                    
                    await _produitService.AjouterProduitAsync(produit);
                    MessageBox.Show("Produit ajouté avec succès !",
                                  "Succès", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    // Modification
                    produit.DateModification = DateTime.Now;
                    produit.UtilisateurModification = _utilisateurConnecte.NomUtilisateur;
                    
                    await _produitService.ModifierProduitAsync(produit);
                    MessageBox.Show("Produit modifié avec succès !",
                                  "Succès", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }

                ActiverModeEdition(false);
                ChargerProduits();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors de l'enregistrement: {ex.Message}",
                              "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnAnnuler_Click(object sender, EventArgs e)
        {
            ActiverModeEdition(false);
            if (_produitSelectionne != null)
            {
                AfficherProduit(_produitSelectionne);
            }
            else
            {
                ViderChamps();
            }
        }

        private async void btnSupprimer_Click(object sender, EventArgs e)
        {
            if (_produitSelectionne != null)
            {
                var result = MessageBox.Show(
                    $"Êtes-vous sûr de vouloir supprimer le produit '{_produitSelectionne.Nom}' ?",
                    "Confirmation de suppression",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    try
                    {
                        await _produitService.SupprimerProduitAsync(_produitSelectionne.Id);
                        MessageBox.Show("Produit supprimé avec succès !",
                                      "Succès", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        ChargerProduits();
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"Erreur lors de la suppression: {ex.Message}",
                                      "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
        }

        private void btnActualiser_Click(object sender, EventArgs e)
        {
            ChargerProduits();
        }

        private void btnFermer_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void txtRecherche_TextChanged(object sender, EventArgs e)
        {
            FiltrerProduits();
        }

        private void FiltrerProduits()
        {
            if (_produits == null) return;

            var termeRecherche = txtRecherche.Text.ToLower();
            
            if (string.IsNullOrWhiteSpace(termeRecherche))
            {
                dgvProduits.DataSource = _produits;
            }
            else
            {
                var produitsFiltres = _produits.Where(p =>
                    (p.CodeBarre?.ToLower().Contains(termeRecherche) ?? false) ||
                    (p.Nom?.ToLower().Contains(termeRecherche) ?? false) ||
                    (p.Description?.ToLower().Contains(termeRecherche) ?? false) ||
                    (p.Marque?.ToLower().Contains(termeRecherche) ?? false)
                ).ToList();
                
                dgvProduits.DataSource = produitsFiltres;
            }
        }

        private void FRM_PRODUITS_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (_modeEdition)
            {
                var result = MessageBox.Show(
                    "Des modifications sont en cours. Voulez-vous les abandonner ?",
                    "Modifications en cours",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result == DialogResult.No)
                {
                    e.Cancel = true;
                }
            }
        }
    }
}

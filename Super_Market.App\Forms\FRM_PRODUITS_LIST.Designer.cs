namespace Super_Market.App.Forms
{
    partial class FRM_PRODUITS_LIST
    {
        private System.ComponentModel.IContainer components = null;

        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        private void InitializeComponent()
        {
            this.panelMain = new Krypton.Toolkit.KryptonPanel();
            this.dgvProduits = new Krypton.Toolkit.KryptonDataGridView();
            this.panelTop = new Krypton.Toolkit.KryptonPanel();
            this.lblTitre = new Krypton.Toolkit.KryptonLabel();
            this.panelRecherche = new Krypton.Toolkit.KryptonPanel();
            this.lblRecherche = new Krypton.Toolkit.KryptonLabel();
            this.txtRecherche = new Krypton.Toolkit.KryptonTextBox();
            this.lblCategorie = new Krypton.Toolkit.KryptonLabel();
            this.cmbFiltreCategorie = new Krypton.Toolkit.KryptonComboBox();
            this.chkSeulementActifs = new Krypton.Toolkit.KryptonCheckBox();
            this.btnActualiser = new Krypton.Toolkit.KryptonButton();
            this.panelBoutons = new Krypton.Toolkit.KryptonPanel();
            this.btnAjouter = new Krypton.Toolkit.KryptonButton();
            this.btnModifier = new Krypton.Toolkit.KryptonButton();
            this.btnVoir = new Krypton.Toolkit.KryptonButton();
            this.btnSupprimer = new Krypton.Toolkit.KryptonButton();
            this.btnStock = new Krypton.Toolkit.KryptonButton();
            this.btnExporter = new Krypton.Toolkit.KryptonButton();
            this.btnFermer = new Krypton.Toolkit.KryptonButton();
            this.statusStrip = new System.Windows.Forms.StatusStrip();
            this.lblStatus = new System.Windows.Forms.ToolStripStatusLabel();
            this.progressBar = new System.Windows.Forms.ToolStripProgressBar();
            ((System.ComponentModel.ISupportInitialize)(this.panelMain)).BeginInit();
            this.panelMain.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgvProduits)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelTop)).BeginInit();
            this.panelTop.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.panelRecherche)).BeginInit();
            this.panelRecherche.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cmbFiltreCategorie)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelBoutons)).BeginInit();
            this.panelBoutons.SuspendLayout();
            this.statusStrip.SuspendLayout();
            this.SuspendLayout();
            // 
            // panelMain
            // 
            this.panelMain.Controls.Add(this.dgvProduits);
            this.panelMain.Controls.Add(this.panelRecherche);
            this.panelMain.Controls.Add(this.panelTop);
            this.panelMain.Controls.Add(this.panelBoutons);
            this.panelMain.Controls.Add(this.statusStrip);
            this.panelMain.Dock = System.Windows.Forms.DockStyle.Fill;
            this.panelMain.Location = new System.Drawing.Point(0, 0);
            this.panelMain.Name = "panelMain";
            this.panelMain.Size = new System.Drawing.Size(1200, 700);
            this.panelMain.TabIndex = 0;
            // 
            // dgvProduits
            // 
            this.dgvProduits.AllowUserToAddRows = false;
            this.dgvProduits.AllowUserToDeleteRows = false;
            this.dgvProduits.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dgvProduits.Location = new System.Drawing.Point(0, 120);
            this.dgvProduits.Name = "dgvProduits";
            this.dgvProduits.ReadOnly = true;
            this.dgvProduits.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.dgvProduits.Size = new System.Drawing.Size(1200, 510);
            this.dgvProduits.TabIndex = 0;
            this.dgvProduits.SelectionChanged += new System.EventHandler(this.dgvProduits_SelectionChanged);
            this.dgvProduits.DoubleClick += new System.EventHandler(this.dgvProduits_DoubleClick);
            this.dgvProduits.KeyDown += new System.Windows.Forms.KeyEventHandler(this.dgvProduits_KeyDown);
            // 
            // panelTop
            // 
            this.panelTop.Controls.Add(this.lblTitre);
            this.panelTop.Dock = System.Windows.Forms.DockStyle.Top;
            this.panelTop.Location = new System.Drawing.Point(0, 0);
            this.panelTop.Name = "panelTop";
            this.panelTop.Size = new System.Drawing.Size(1200, 60);
            this.panelTop.TabIndex = 1;
            // 
            // lblTitre
            // 
            this.lblTitre.Location = new System.Drawing.Point(20, 20);
            this.lblTitre.Name = "lblTitre";
            this.lblTitre.Size = new System.Drawing.Size(180, 25);
            this.lblTitre.StateCommon.ShortText.Font = new System.Drawing.Font("Segoe UI", 14F, System.Drawing.FontStyle.Bold);
            this.lblTitre.StateCommon.ShortText.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(122)))), ((int)(((byte)(204)))));
            this.lblTitre.TabIndex = 0;
            this.lblTitre.Values.Text = "📦 Liste des Produits";
            // 
            // panelRecherche
            // 
            this.panelRecherche.Controls.Add(this.btnActualiser);
            this.panelRecherche.Controls.Add(this.chkSeulementActifs);
            this.panelRecherche.Controls.Add(this.cmbFiltreCategorie);
            this.panelRecherche.Controls.Add(this.lblCategorie);
            this.panelRecherche.Controls.Add(this.txtRecherche);
            this.panelRecherche.Controls.Add(this.lblRecherche);
            this.panelRecherche.Dock = System.Windows.Forms.DockStyle.Top;
            this.panelRecherche.Location = new System.Drawing.Point(0, 60);
            this.panelRecherche.Name = "panelRecherche";
            this.panelRecherche.Size = new System.Drawing.Size(1200, 60);
            this.panelRecherche.TabIndex = 2;
            // 
            // lblRecherche
            // 
            this.lblRecherche.Location = new System.Drawing.Point(20, 20);
            this.lblRecherche.Name = "lblRecherche";
            this.lblRecherche.Size = new System.Drawing.Size(70, 20);
            this.lblRecherche.TabIndex = 0;
            this.lblRecherche.Values.Text = "🔍 Recherche:";
            // 
            // txtRecherche
            // 
            this.txtRecherche.Location = new System.Drawing.Point(120, 18);
            this.txtRecherche.Name = "txtRecherche";
            this.txtRecherche.Size = new System.Drawing.Size(300, 23);
            this.txtRecherche.TabIndex = 1;
            this.txtRecherche.TextChanged += new System.EventHandler(this.txtRecherche_TextChanged);
            // 
            // lblCategorie
            // 
            this.lblCategorie.Location = new System.Drawing.Point(440, 20);
            this.lblCategorie.Name = "lblCategorie";
            this.lblCategorie.Size = new System.Drawing.Size(65, 20);
            this.lblCategorie.TabIndex = 2;
            this.lblCategorie.Values.Text = "Catégorie:";
            // 
            // cmbFiltreCategorie
            // 
            this.cmbFiltreCategorie.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmbFiltreCategorie.DropDownWidth = 200;
            this.cmbFiltreCategorie.Location = new System.Drawing.Point(520, 18);
            this.cmbFiltreCategorie.Name = "cmbFiltreCategorie";
            this.cmbFiltreCategorie.Size = new System.Drawing.Size(200, 21);
            this.cmbFiltreCategorie.TabIndex = 3;
            this.cmbFiltreCategorie.SelectedIndexChanged += new System.EventHandler(this.cmbFiltreCategorie_SelectedIndexChanged);
            // 
            // chkSeulementActifs
            // 
            this.chkSeulementActifs.Checked = true;
            this.chkSeulementActifs.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkSeulementActifs.Location = new System.Drawing.Point(740, 20);
            this.chkSeulementActifs.Name = "chkSeulementActifs";
            this.chkSeulementActifs.Size = new System.Drawing.Size(120, 20);
            this.chkSeulementActifs.TabIndex = 4;
            this.chkSeulementActifs.Values.Text = "Seulement actifs";
            this.chkSeulementActifs.CheckedChanged += new System.EventHandler(this.chkSeulementActifs_CheckedChanged);
            // 
            // btnActualiser
            // 
            this.btnActualiser.Location = new System.Drawing.Point(880, 15);
            this.btnActualiser.Name = "btnActualiser";
            this.btnActualiser.Size = new System.Drawing.Size(100, 30);
            this.btnActualiser.TabIndex = 5;
            this.btnActualiser.Values.Text = "🔄 Actualiser";
            this.btnActualiser.Click += new System.EventHandler(this.btnActualiser_Click);
            // 
            // panelBoutons
            // 
            this.panelBoutons.Controls.Add(this.btnFermer);
            this.panelBoutons.Controls.Add(this.btnExporter);
            this.panelBoutons.Controls.Add(this.btnStock);
            this.panelBoutons.Controls.Add(this.btnSupprimer);
            this.panelBoutons.Controls.Add(this.btnVoir);
            this.panelBoutons.Controls.Add(this.btnModifier);
            this.panelBoutons.Controls.Add(this.btnAjouter);
            this.panelBoutons.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.panelBoutons.Location = new System.Drawing.Point(0, 630);
            this.panelBoutons.Name = "panelBoutons";
            this.panelBoutons.Size = new System.Drawing.Size(1200, 48);
            this.panelBoutons.TabIndex = 3;
            // 
            // btnAjouter
            // 
            this.btnAjouter.Location = new System.Drawing.Point(20, 8);
            this.btnAjouter.Name = "btnAjouter";
            this.btnAjouter.Size = new System.Drawing.Size(100, 32);
            this.btnAjouter.StateCommon.Back.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(122)))), ((int)(((byte)(204)))));
            this.btnAjouter.StateCommon.Back.Color2 = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(122)))), ((int)(((byte)(204)))));
            this.btnAjouter.StateCommon.Content.ShortText.Color1 = System.Drawing.Color.White;
            this.btnAjouter.TabIndex = 0;
            this.btnAjouter.Values.Text = "➕ Ajouter";
            this.btnAjouter.Click += new System.EventHandler(this.btnAjouter_Click);
            // 
            // btnModifier
            // 
            this.btnModifier.Location = new System.Drawing.Point(130, 8);
            this.btnModifier.Name = "btnModifier";
            this.btnModifier.Size = new System.Drawing.Size(100, 32);
            this.btnModifier.TabIndex = 1;
            this.btnModifier.Values.Text = "✏️ Modifier";
            this.btnModifier.Click += new System.EventHandler(this.btnModifier_Click);
            // 
            // btnVoir
            // 
            this.btnVoir.Location = new System.Drawing.Point(240, 8);
            this.btnVoir.Name = "btnVoir";
            this.btnVoir.Size = new System.Drawing.Size(100, 32);
            this.btnVoir.TabIndex = 2;
            this.btnVoir.Values.Text = "👁️ Voir";
            this.btnVoir.Click += new System.EventHandler(this.btnVoir_Click);
            // 
            // btnSupprimer
            // 
            this.btnSupprimer.Location = new System.Drawing.Point(350, 8);
            this.btnSupprimer.Name = "btnSupprimer";
            this.btnSupprimer.Size = new System.Drawing.Size(100, 32);
            this.btnSupprimer.StateCommon.Back.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(220)))), ((int)(((byte)(53)))), ((int)(((byte)(69)))));
            this.btnSupprimer.StateCommon.Back.Color2 = System.Drawing.Color.FromArgb(((int)(((byte)(220)))), ((int)(((byte)(53)))), ((int)(((byte)(69)))));
            this.btnSupprimer.StateCommon.Content.ShortText.Color1 = System.Drawing.Color.White;
            this.btnSupprimer.TabIndex = 3;
            this.btnSupprimer.Values.Text = "🗑️ Supprimer";
            this.btnSupprimer.Click += new System.EventHandler(this.btnSupprimer_Click);
            // 
            // btnStock
            // 
            this.btnStock.Location = new System.Drawing.Point(460, 8);
            this.btnStock.Name = "btnStock";
            this.btnStock.Size = new System.Drawing.Size(100, 32);
            this.btnStock.StateCommon.Back.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(193)))), ((int)(((byte)(7)))));
            this.btnStock.StateCommon.Back.Color2 = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(193)))), ((int)(((byte)(7)))));
            this.btnStock.StateCommon.Content.ShortText.Color1 = System.Drawing.Color.Black;
            this.btnStock.TabIndex = 4;
            this.btnStock.Values.Text = "📊 Stock";
            this.btnStock.Click += new System.EventHandler(this.btnStock_Click);
            // 
            // btnExporter
            // 
            this.btnExporter.Location = new System.Drawing.Point(580, 8);
            this.btnExporter.Name = "btnExporter";
            this.btnExporter.Size = new System.Drawing.Size(100, 32);
            this.btnExporter.StateCommon.Back.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(40)))), ((int)(((byte)(167)))), ((int)(((byte)(69)))));
            this.btnExporter.StateCommon.Back.Color2 = System.Drawing.Color.FromArgb(((int)(((byte)(40)))), ((int)(((byte)(167)))), ((int)(((byte)(69)))));
            this.btnExporter.StateCommon.Content.ShortText.Color1 = System.Drawing.Color.White;
            this.btnExporter.TabIndex = 5;
            this.btnExporter.Values.Text = "📤 Exporter";
            this.btnExporter.Click += new System.EventHandler(this.btnExporter_Click);
            // 
            // btnFermer
            // 
            this.btnFermer.Location = new System.Drawing.Point(1080, 8);
            this.btnFermer.Name = "btnFermer";
            this.btnFermer.Size = new System.Drawing.Size(100, 32);
            this.btnFermer.TabIndex = 6;
            this.btnFermer.Values.Text = "🚪 Fermer";
            this.btnFermer.Click += new System.EventHandler(this.btnFermer_Click);
            // 
            // statusStrip
            // 
            this.statusStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.lblStatus,
            this.progressBar});
            this.statusStrip.Location = new System.Drawing.Point(0, 678);
            this.statusStrip.Name = "statusStrip";
            this.statusStrip.Size = new System.Drawing.Size(1200, 22);
            this.statusStrip.TabIndex = 4;
            this.statusStrip.Text = "statusStrip1";
            // 
            // lblStatus
            // 
            this.lblStatus.Name = "lblStatus";
            this.lblStatus.Size = new System.Drawing.Size(26, 17);
            this.lblStatus.Text = "Prêt";
            // 
            // progressBar
            // 
            this.progressBar.Name = "progressBar";
            this.progressBar.Size = new System.Drawing.Size(100, 16);
            this.progressBar.Visible = false;
            // 
            // FRM_PRODUITS_LIST
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1200, 700);
            this.Controls.Add(this.panelMain);
            this.KeyPreview = true;
            this.Name = "FRM_PRODUITS_LIST";
            this.Text = "Liste des Produits";
            this.Load += new System.EventHandler(this.FRM_PRODUITS_LIST_Load);
            ((System.ComponentModel.ISupportInitialize)(this.panelMain)).EndInit();
            this.panelMain.ResumeLayout(false);
            this.panelMain.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgvProduits)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelTop)).EndInit();
            this.panelTop.ResumeLayout(false);
            this.panelTop.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.panelRecherche)).EndInit();
            this.panelRecherche.ResumeLayout(false);
            this.panelRecherche.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cmbFiltreCategorie)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelBoutons)).EndInit();
            this.panelBoutons.ResumeLayout(false);
            this.statusStrip.ResumeLayout(false);
            this.statusStrip.PerformLayout();
            this.ResumeLayout(false);
        }

        #endregion

        private Krypton.Toolkit.KryptonPanel panelMain;
        private Krypton.Toolkit.KryptonDataGridView dgvProduits;
        private Krypton.Toolkit.KryptonPanel panelTop;
        private Krypton.Toolkit.KryptonLabel lblTitre;
        private Krypton.Toolkit.KryptonPanel panelRecherche;
        private Krypton.Toolkit.KryptonLabel lblRecherche;
        private Krypton.Toolkit.KryptonTextBox txtRecherche;
        private Krypton.Toolkit.KryptonLabel lblCategorie;
        private Krypton.Toolkit.KryptonComboBox cmbFiltreCategorie;
        private Krypton.Toolkit.KryptonCheckBox chkSeulementActifs;
        private Krypton.Toolkit.KryptonButton btnActualiser;
        private Krypton.Toolkit.KryptonPanel panelBoutons;
        private Krypton.Toolkit.KryptonButton btnAjouter;
        private Krypton.Toolkit.KryptonButton btnModifier;
        private Krypton.Toolkit.KryptonButton btnVoir;
        private Krypton.Toolkit.KryptonButton btnSupprimer;
        private Krypton.Toolkit.KryptonButton btnStock;
        private Krypton.Toolkit.KryptonButton btnExporter;
        private Krypton.Toolkit.KryptonButton btnFermer;
        private System.Windows.Forms.StatusStrip statusStrip;
        private System.Windows.Forms.ToolStripStatusLabel lblStatus;
        private System.Windows.Forms.ToolStripProgressBar progressBar;
    }
}

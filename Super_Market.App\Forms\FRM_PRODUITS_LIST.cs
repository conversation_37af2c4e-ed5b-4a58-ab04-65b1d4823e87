using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using Krypton.Toolkit;
using Super_Market.Business.Services;
using Super_Market.Core.Models;

namespace Super_Market.App.Forms
{
    public partial class FRM_PRODUITS_LIST : KryptonForm
    {
        private readonly ProduitService _produitService;
        private readonly CategorieService _categorieService;
        private readonly Utilisateur _utilisateurConnecte;
        private List<Produit> _produits;
        private Produit _produitSelectionne;

        public FRM_PRODUITS_LIST(Utilisateur utilisateur)
        {
            InitializeComponent();
            _produitService = new ProduitService();
            _categorieService = new CategorieService();
            _utilisateurConnecte = utilisateur;
            InitialiserFormulaire();
        }

        private void InitialiserFormulaire()
        {
            this.Text = "Liste des Produits";
            this.WindowState = FormWindowState.Maximized;
            
            // Configurer la grille
            ConfigurerGrille();
            
            // Charger les données
            ChargerProduits();
            ChargerFiltreCategories();
        }

        private void ConfigurerGrille()
        {
            dgvProduits.AutoGenerateColumns = false;
            dgvProduits.AllowUserToAddRows = false;
            dgvProduits.AllowUserToDeleteRows = false;
            dgvProduits.ReadOnly = true;
            dgvProduits.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvProduits.MultiSelect = false;

            dgvProduits.Columns.Clear();
            
            dgvProduits.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Id",
                HeaderText = "ID",
                DataPropertyName = "Id",
                Width = 60,
                Visible = false
            });

            dgvProduits.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "CodeBarre",
                HeaderText = "Code-Barres",
                DataPropertyName = "CodeBarre",
                Width = 120
            });

            dgvProduits.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Nom",
                HeaderText = "Nom du Produit",
                DataPropertyName = "Nom",
                Width = 250
            });

            dgvProduits.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Categorie",
                HeaderText = "Catégorie",
                DataPropertyName = "CategorieNom",
                Width = 150
            });

            dgvProduits.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Marque",
                HeaderText = "Marque",
                DataPropertyName = "Marque",
                Width = 120
            });

            dgvProduits.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "PrixAchat",
                HeaderText = "Prix Achat",
                DataPropertyName = "PrixAchat",
                Width = 100,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "F2" }
            });

            dgvProduits.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "PrixVente",
                HeaderText = "Prix Vente",
                DataPropertyName = "PrixVente",
                Width = 100,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "F2" }
            });

            dgvProduits.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Unite",
                HeaderText = "Unité",
                DataPropertyName = "Unite",
                Width = 80
            });

            dgvProduits.Columns.Add(new DataGridViewCheckBoxColumn
            {
                Name = "EstActif",
                HeaderText = "Actif",
                DataPropertyName = "EstActif",
                Width = 60
            });

            dgvProduits.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "DateCreation",
                HeaderText = "Date Création",
                DataPropertyName = "DateCreation",
                Width = 120,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "dd/MM/yyyy" }
            });
        }

        private async void ChargerProduits()
        {
            try
            {
                lblStatus.Text = "Chargement des produits...";
                progressBar.Visible = true;
                btnActualiser.Enabled = false;
                
                _produits = (await _produitService.GetAllProduitsAsync()).ToList();
                
                // Enrichir avec les noms des catégories
                var categories = await _categorieService.GetAllCategoriesAsync();
                foreach (var produit in _produits)
                {
                    var categorie = categories.FirstOrDefault(c => c.Id == produit.CategorieId);
                    if (categorie != null)
                    {
                        // Utiliser une propriété dynamique pour l'affichage
                        ((dynamic)produit).CategorieNom = categorie.Nom;
                    }
                    else
                    {
                        ((dynamic)produit).CategorieNom = "Non définie";
                    }
                }
                
                dgvProduits.DataSource = _produits;
                
                lblStatus.Text = $"{_produits.Count} produit(s) chargé(s)";
                progressBar.Visible = false;
                btnActualiser.Enabled = true;
                
                // Activer/désactiver les boutons selon la sélection
                ActiverBoutons();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors du chargement des produits: {ex.Message}",
                              "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                lblStatus.Text = "Erreur de chargement";
                progressBar.Visible = false;
                btnActualiser.Enabled = true;
            }
        }

        private async void ChargerFiltreCategories()
        {
            try
            {
                var categories = await _categorieService.GetAllCategoriesAsync();
                
                cmbFiltreCategorie.Items.Clear();
                cmbFiltreCategorie.Items.Add(new { Id = 0, Nom = "Toutes les catégories" });
                
                foreach (var categorie in categories.Where(c => c.EstActif))
                {
                    cmbFiltreCategorie.Items.Add(new { Id = categorie.Id, Nom = categorie.Nom });
                }
                
                cmbFiltreCategorie.DisplayMember = "Nom";
                cmbFiltreCategorie.ValueMember = "Id";
                cmbFiltreCategorie.SelectedIndex = 0;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors du chargement des catégories: {ex.Message}",
                              "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void dgvProduits_SelectionChanged(object sender, EventArgs e)
        {
            if (dgvProduits.SelectedRows.Count > 0)
            {
                var index = dgvProduits.SelectedRows[0].Index;
                if (index >= 0 && index < _produits.Count)
                {
                    _produitSelectionne = _produits[index];
                }
            }
            else
            {
                _produitSelectionne = null;
            }
            
            ActiverBoutons();
        }

        private void ActiverBoutons()
        {
            bool produitSelectionne = _produitSelectionne != null;
            btnModifier.Enabled = produitSelectionne;
            btnSupprimer.Enabled = produitSelectionne;
            btnVoir.Enabled = produitSelectionne;
            btnStock.Enabled = produitSelectionne;
        }

        private void btnAjouter_Click(object sender, EventArgs e)
        {
            using (var formAjout = new FRM_PRODUIT_EDIT(_utilisateurConnecte))
            {
                if (formAjout.ShowDialog() == DialogResult.OK)
                {
                    ChargerProduits();
                }
            }
        }

        private void btnModifier_Click(object sender, EventArgs e)
        {
            if (_produitSelectionne != null)
            {
                using (var formModif = new FRM_PRODUIT_EDIT(_utilisateurConnecte, _produitSelectionne))
                {
                    if (formModif.ShowDialog() == DialogResult.OK)
                    {
                        ChargerProduits();
                    }
                }
            }
        }

        private void btnVoir_Click(object sender, EventArgs e)
        {
            if (_produitSelectionne != null)
            {
                using (var formVoir = new FRM_PRODUIT_EDIT(_utilisateurConnecte, _produitSelectionne, true))
                {
                    formVoir.ShowDialog();
                }
            }
        }

        private void btnStock_Click(object sender, EventArgs e)
        {
            if (_produitSelectionne != null)
            {
                // TODO: Ouvrir le formulaire de gestion du stock pour ce produit
                MessageBox.Show($"Gestion du stock pour: {_produitSelectionne.Nom}",
                              "Stock", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private async void btnSupprimer_Click(object sender, EventArgs e)
        {
            if (_produitSelectionne != null)
            {
                var result = MessageBox.Show(
                    $"Êtes-vous sûr de vouloir supprimer le produit '{_produitSelectionne.Nom}' ?\n\n" +
                    "Cette action est irréversible.",
                    "Confirmation de suppression",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    try
                    {
                        lblStatus.Text = "Suppression en cours...";
                        await _produitService.SupprimerProduitAsync(_produitSelectionne.Id);
                        
                        MessageBox.Show("Produit supprimé avec succès !",
                                      "Succès", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        
                        ChargerProduits();
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"Erreur lors de la suppression: {ex.Message}",
                                      "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        lblStatus.Text = "Erreur de suppression";
                    }
                }
            }
        }

        private void btnActualiser_Click(object sender, EventArgs e)
        {
            ChargerProduits();
        }

        private void btnFermer_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void txtRecherche_TextChanged(object sender, EventArgs e)
        {
            FiltrerProduits();
        }

        private void cmbFiltreCategorie_SelectedIndexChanged(object sender, EventArgs e)
        {
            FiltrerProduits();
        }

        private void chkSeulementActifs_CheckedChanged(object sender, EventArgs e)
        {
            FiltrerProduits();
        }

        private void FiltrerProduits()
        {
            if (_produits == null) return;

            var termeRecherche = txtRecherche.Text.ToLower();
            var categorieSelectionnee = (dynamic)cmbFiltreCategorie.SelectedItem;
            var categorieId = categorieSelectionnee?.Id ?? 0;
            var seulementActifs = chkSeulementActifs.Checked;
            
            var produitsFiltres = _produits.Where(p =>
            {
                // Filtre par terme de recherche
                bool correspondRecherche = string.IsNullOrWhiteSpace(termeRecherche) ||
                    (p.CodeBarre?.ToLower().Contains(termeRecherche) ?? false) ||
                    (p.Nom?.ToLower().Contains(termeRecherche) ?? false) ||
                    (p.Description?.ToLower().Contains(termeRecherche) ?? false) ||
                    (p.Marque?.ToLower().Contains(termeRecherche) ?? false);
                
                // Filtre par catégorie
                bool correspondCategorie = categorieId == 0 || p.CategorieId == categorieId;
                
                // Filtre par statut actif
                bool correspondStatut = !seulementActifs || p.EstActif;
                
                return correspondRecherche && correspondCategorie && correspondStatut;
            }).ToList();
            
            dgvProduits.DataSource = produitsFiltres;
            lblStatus.Text = $"{produitsFiltres.Count} produit(s) affiché(s) sur {_produits.Count}";
        }

        private void dgvProduits_DoubleClick(object sender, EventArgs e)
        {
            if (_produitSelectionne != null)
            {
                btnModifier_Click(sender, e);
            }
        }

        private void FRM_PRODUITS_LIST_Load(object sender, EventArgs e)
        {
            // Focus sur la recherche
            txtRecherche.Focus();
        }

        private void dgvProduits_KeyDown(object sender, KeyEventArgs e)
        {
            switch (e.KeyCode)
            {
                case Keys.Enter:
                    if (_produitSelectionne != null)
                    {
                        btnModifier_Click(sender, e);
                        e.Handled = true;
                    }
                    break;
                    
                case Keys.Delete:
                    if (_produitSelectionne != null)
                    {
                        btnSupprimer_Click(sender, e);
                        e.Handled = true;
                    }
                    break;
                    
                case Keys.F5:
                    btnActualiser_Click(sender, e);
                    e.Handled = true;
                    break;
                    
                case Keys.F2:
                    if (_produitSelectionne != null)
                    {
                        btnModifier_Click(sender, e);
                        e.Handled = true;
                    }
                    break;
            }
        }

        private void btnExporter_Click(object sender, EventArgs e)
        {
            try
            {
                if (_produits == null || _produits.Count == 0)
                {
                    MessageBox.Show("Aucune donnée à exporter.", "Information", 
                                  MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                using (var saveDialog = new SaveFileDialog())
                {
                    saveDialog.Filter = "Fichiers CSV (*.csv)|*.csv|Tous les fichiers (*.*)|*.*";
                    saveDialog.Title = "Exporter les produits";
                    saveDialog.FileName = $"Produits_{DateTime.Now:yyyyMMdd_HHmmss}.csv";

                    if (saveDialog.ShowDialog() == DialogResult.OK)
                    {
                        ExporterVersCsv(saveDialog.FileName);
                        MessageBox.Show("Export réalisé avec succès !", "Succès", 
                                      MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors de l'export: {ex.Message}", "Erreur", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ExporterVersCsv(string cheminFichier)
        {
            using (var writer = new System.IO.StreamWriter(cheminFichier, false, System.Text.Encoding.UTF8))
            {
                // En-têtes
                writer.WriteLine("Code-Barres;Nom;Description;Catégorie;Marque;Prix Achat;Prix Vente;Unité;Actif;Date Création");
                
                // Données
                foreach (var produit in _produits)
                {
                    var categorieNom = ((dynamic)produit).CategorieNom ?? "";
                    
                    writer.WriteLine($"{produit.CodeBarre};{produit.Nom};{produit.Description};" +
                                   $"{categorieNom};{produit.Marque};{produit.PrixAchat:F2};{produit.PrixVente:F2};" +
                                   $"{produit.Unite};{(produit.EstActif ? "Oui" : "Non")};" +
                                   $"{produit.DateCreation:dd/MM/yyyy}");
                }
            }
        }
    }
}

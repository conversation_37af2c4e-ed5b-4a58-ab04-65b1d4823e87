namespace Super_Market.App.Forms
{
    partial class FRM_PRODUIT_EDIT
    {
        private System.ComponentModel.IContainer components = null;

        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        private void InitializeComponent()
        {
            this.panelMain = new Krypton.Toolkit.KryptonPanel();
            this.groupBoxDetails = new Krypton.Toolkit.KryptonGroupBox();
            this.lblCodeBarre = new Krypton.Toolkit.KryptonLabel();
            this.txtCodeBarre = new Krypton.Toolkit.KryptonTextBox();
            this.lblCodeInterne = new Krypton.Toolkit.KryptonLabel();
            this.txtCodeInterne = new Krypton.Toolkit.KryptonTextBox();
            this.lblNom = new Krypton.Toolkit.KryptonLabel();
            this.txtNom = new Krypton.Toolkit.KryptonTextBox();
            this.lblDescription = new Krypton.Toolkit.KryptonLabel();
            this.txtDescription = new Krypton.Toolkit.KryptonTextBox();
            this.lblMarque = new Krypton.Toolkit.KryptonLabel();
            this.txtMarque = new Krypton.Toolkit.KryptonTextBox();
            this.lblCategorie = new Krypton.Toolkit.KryptonLabel();
            this.cmbCategorie = new Krypton.Toolkit.KryptonComboBox();
            this.lblUnite = new Krypton.Toolkit.KryptonLabel();
            this.cmbUnite = new Krypton.Toolkit.KryptonComboBox();
            this.lblPrixAchat = new Krypton.Toolkit.KryptonLabel();
            this.numPrixAchat = new Krypton.Toolkit.KryptonNumericUpDown();
            this.lblPrixVente = new Krypton.Toolkit.KryptonLabel();
            this.numPrixVente = new Krypton.Toolkit.KryptonNumericUpDown();
            this.lblStockMinimum = new Krypton.Toolkit.KryptonLabel();
            this.numStockMinimum = new Krypton.Toolkit.KryptonNumericUpDown();
            this.lblTauxTVA = new Krypton.Toolkit.KryptonLabel();
            this.numTauxTVA = new Krypton.Toolkit.KryptonNumericUpDown();
            this.chkActif = new Krypton.Toolkit.KryptonCheckBox();
            this.panelInfo = new Krypton.Toolkit.KryptonPanel();
            this.lblInfoCreation = new Krypton.Toolkit.KryptonLabel();
            this.lblInfoModification = new Krypton.Toolkit.KryptonLabel();
            this.panelBoutons = new Krypton.Toolkit.KryptonPanel();
            this.btnEnregistrer = new Krypton.Toolkit.KryptonButton();
            this.btnAnnuler = new Krypton.Toolkit.KryptonButton();
            ((System.ComponentModel.ISupportInitialize)(this.panelMain)).BeginInit();
            this.panelMain.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupBoxDetails)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupBoxDetails.Panel)).BeginInit();
            this.groupBoxDetails.Panel.SuspendLayout();
            this.groupBoxDetails.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cmbCategorie)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cmbUnite)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelInfo)).BeginInit();
            this.panelInfo.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.panelBoutons)).BeginInit();
            this.panelBoutons.SuspendLayout();
            this.SuspendLayout();
            // 
            // panelMain
            // 
            this.panelMain.Controls.Add(this.groupBoxDetails);
            this.panelMain.Controls.Add(this.panelInfo);
            this.panelMain.Controls.Add(this.panelBoutons);
            this.panelMain.Dock = System.Windows.Forms.DockStyle.Fill;
            this.panelMain.Location = new System.Drawing.Point(0, 0);
            this.panelMain.Name = "panelMain";
            this.panelMain.Padding = new System.Windows.Forms.Padding(10);
            this.panelMain.Size = new System.Drawing.Size(800, 600);
            this.panelMain.TabIndex = 0;
            // 
            // groupBoxDetails
            // 
            this.groupBoxDetails.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupBoxDetails.Location = new System.Drawing.Point(10, 10);
            this.groupBoxDetails.Name = "groupBoxDetails";
            // 
            // groupBoxDetails.Panel
            // 
            this.groupBoxDetails.Panel.Controls.Add(this.chkActif);
            this.groupBoxDetails.Panel.Controls.Add(this.numTauxTVA);
            this.groupBoxDetails.Panel.Controls.Add(this.lblTauxTVA);
            this.groupBoxDetails.Panel.Controls.Add(this.numStockMinimum);
            this.groupBoxDetails.Panel.Controls.Add(this.lblStockMinimum);
            this.groupBoxDetails.Panel.Controls.Add(this.numPrixVente);
            this.groupBoxDetails.Panel.Controls.Add(this.lblPrixVente);
            this.groupBoxDetails.Panel.Controls.Add(this.numPrixAchat);
            this.groupBoxDetails.Panel.Controls.Add(this.lblPrixAchat);
            this.groupBoxDetails.Panel.Controls.Add(this.cmbUnite);
            this.groupBoxDetails.Panel.Controls.Add(this.lblUnite);
            this.groupBoxDetails.Panel.Controls.Add(this.cmbCategorie);
            this.groupBoxDetails.Panel.Controls.Add(this.lblCategorie);
            this.groupBoxDetails.Panel.Controls.Add(this.txtMarque);
            this.groupBoxDetails.Panel.Controls.Add(this.lblMarque);
            this.groupBoxDetails.Panel.Controls.Add(this.txtDescription);
            this.groupBoxDetails.Panel.Controls.Add(this.lblDescription);
            this.groupBoxDetails.Panel.Controls.Add(this.txtNom);
            this.groupBoxDetails.Panel.Controls.Add(this.lblNom);
            this.groupBoxDetails.Panel.Controls.Add(this.txtCodeInterne);
            this.groupBoxDetails.Panel.Controls.Add(this.lblCodeInterne);
            this.groupBoxDetails.Panel.Controls.Add(this.txtCodeBarre);
            this.groupBoxDetails.Panel.Controls.Add(this.lblCodeBarre);
            this.groupBoxDetails.Size = new System.Drawing.Size(780, 470);
            this.groupBoxDetails.TabIndex = 0;
            this.groupBoxDetails.Values.Heading = "Informations du Produit";
            // 
            // lblCodeBarre
            // 
            this.lblCodeBarre.Location = new System.Drawing.Point(20, 20);
            this.lblCodeBarre.Name = "lblCodeBarre";
            this.lblCodeBarre.Size = new System.Drawing.Size(75, 20);
            this.lblCodeBarre.TabIndex = 0;
            this.lblCodeBarre.Values.Text = "Code-Barres:";
            // 
            // txtCodeBarre
            // 
            this.txtCodeBarre.Location = new System.Drawing.Point(20, 45);
            this.txtCodeBarre.Name = "txtCodeBarre";
            this.txtCodeBarre.Size = new System.Drawing.Size(200, 23);
            this.txtCodeBarre.TabIndex = 1;
            // 
            // lblCodeInterne
            // 
            this.lblCodeInterne.Location = new System.Drawing.Point(240, 20);
            this.lblCodeInterne.Name = "lblCodeInterne";
            this.lblCodeInterne.Size = new System.Drawing.Size(80, 20);
            this.lblCodeInterne.TabIndex = 2;
            this.lblCodeInterne.Values.Text = "Code Interne:";
            // 
            // txtCodeInterne
            // 
            this.txtCodeInterne.Location = new System.Drawing.Point(240, 45);
            this.txtCodeInterne.Name = "txtCodeInterne";
            this.txtCodeInterne.Size = new System.Drawing.Size(200, 23);
            this.txtCodeInterne.TabIndex = 3;
            // 
            // lblNom
            // 
            this.lblNom.Location = new System.Drawing.Point(20, 80);
            this.lblNom.Name = "lblNom";
            this.lblNom.Size = new System.Drawing.Size(38, 20);
            this.lblNom.TabIndex = 4;
            this.lblNom.Values.Text = "Nom: *";
            // 
            // txtNom
            // 
            this.txtNom.Location = new System.Drawing.Point(20, 105);
            this.txtNom.Name = "txtNom";
            this.txtNom.Size = new System.Drawing.Size(420, 23);
            this.txtNom.TabIndex = 5;
            // 
            // lblDescription
            // 
            this.lblDescription.Location = new System.Drawing.Point(20, 140);
            this.lblDescription.Name = "lblDescription";
            this.lblDescription.Size = new System.Drawing.Size(74, 20);
            this.lblDescription.TabIndex = 6;
            this.lblDescription.Values.Text = "Description:";
            // 
            // txtDescription
            // 
            this.txtDescription.Location = new System.Drawing.Point(20, 165);
            this.txtDescription.Multiline = true;
            this.txtDescription.Name = "txtDescription";
            this.txtDescription.Size = new System.Drawing.Size(720, 60);
            this.txtDescription.TabIndex = 7;
            // 
            // lblMarque
            // 
            this.lblMarque.Location = new System.Drawing.Point(460, 80);
            this.lblMarque.Name = "lblMarque";
            this.lblMarque.Size = new System.Drawing.Size(50, 20);
            this.lblMarque.TabIndex = 8;
            this.lblMarque.Values.Text = "Marque:";
            // 
            // txtMarque
            // 
            this.txtMarque.Location = new System.Drawing.Point(460, 105);
            this.txtMarque.Name = "txtMarque";
            this.txtMarque.Size = new System.Drawing.Size(280, 23);
            this.txtMarque.TabIndex = 9;
            // 
            // lblCategorie
            // 
            this.lblCategorie.Location = new System.Drawing.Point(20, 240);
            this.lblCategorie.Name = "lblCategorie";
            this.lblCategorie.Size = new System.Drawing.Size(65, 20);
            this.lblCategorie.TabIndex = 10;
            this.lblCategorie.Values.Text = "Catégorie: *";
            // 
            // cmbCategorie
            // 
            this.cmbCategorie.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmbCategorie.DropDownWidth = 300;
            this.cmbCategorie.Location = new System.Drawing.Point(20, 265);
            this.cmbCategorie.Name = "cmbCategorie";
            this.cmbCategorie.Size = new System.Drawing.Size(300, 21);
            this.cmbCategorie.TabIndex = 11;
            // 
            // lblUnite
            // 
            this.lblUnite.Location = new System.Drawing.Point(340, 240);
            this.lblUnite.Name = "lblUnite";
            this.lblUnite.Size = new System.Drawing.Size(40, 20);
            this.lblUnite.TabIndex = 12;
            this.lblUnite.Values.Text = "Unité:";
            // 
            // cmbUnite
            // 
            this.cmbUnite.DropDownWidth = 150;
            this.cmbUnite.Location = new System.Drawing.Point(340, 265);
            this.cmbUnite.Name = "cmbUnite";
            this.cmbUnite.Size = new System.Drawing.Size(150, 21);
            this.cmbUnite.TabIndex = 13;
            // 
            // lblPrixAchat
            // 
            this.lblPrixAchat.Location = new System.Drawing.Point(20, 300);
            this.lblPrixAchat.Name = "lblPrixAchat";
            this.lblPrixAchat.Size = new System.Drawing.Size(70, 20);
            this.lblPrixAchat.TabIndex = 14;
            this.lblPrixAchat.Values.Text = "Prix Achat:";
            // 
            // numPrixAchat
            // 
            this.numPrixAchat.DecimalPlaces = 2;
            this.numPrixAchat.Location = new System.Drawing.Point(20, 325);
            this.numPrixAchat.Maximum = new decimal(new int[] { 999999, 0, 0, 0 });
            this.numPrixAchat.Name = "numPrixAchat";
            this.numPrixAchat.Size = new System.Drawing.Size(120, 22);
            this.numPrixAchat.TabIndex = 15;
            this.numPrixAchat.ValueChanged += new System.EventHandler(this.numPrixAchat_ValueChanged);
            // 
            // lblPrixVente
            // 
            this.lblPrixVente.Location = new System.Drawing.Point(160, 300);
            this.lblPrixVente.Name = "lblPrixVente";
            this.lblPrixVente.Size = new System.Drawing.Size(70, 20);
            this.lblPrixVente.TabIndex = 16;
            this.lblPrixVente.Values.Text = "Prix Vente: *";
            // 
            // numPrixVente
            // 
            this.numPrixVente.DecimalPlaces = 2;
            this.numPrixVente.Location = new System.Drawing.Point(160, 325);
            this.numPrixVente.Maximum = new decimal(new int[] { 999999, 0, 0, 0 });
            this.numPrixVente.Name = "numPrixVente";
            this.numPrixVente.Size = new System.Drawing.Size(120, 22);
            this.numPrixVente.TabIndex = 17;
            // 
            // lblStockMinimum
            // 
            this.lblStockMinimum.Location = new System.Drawing.Point(300, 300);
            this.lblStockMinimum.Name = "lblStockMinimum";
            this.lblStockMinimum.Size = new System.Drawing.Size(90, 20);
            this.lblStockMinimum.TabIndex = 18;
            this.lblStockMinimum.Values.Text = "Stock Minimum:";
            // 
            // numStockMinimum
            // 
            this.numStockMinimum.Location = new System.Drawing.Point(300, 325);
            this.numStockMinimum.Maximum = new decimal(new int[] { 99999, 0, 0, 0 });
            this.numStockMinimum.Name = "numStockMinimum";
            this.numStockMinimum.Size = new System.Drawing.Size(100, 22);
            this.numStockMinimum.TabIndex = 19;
            // 
            // lblTauxTVA
            // 
            this.lblTauxTVA.Location = new System.Drawing.Point(420, 300);
            this.lblTauxTVA.Name = "lblTauxTVA";
            this.lblTauxTVA.Size = new System.Drawing.Size(70, 20);
            this.lblTauxTVA.TabIndex = 20;
            this.lblTauxTVA.Values.Text = "Taux TVA %:";
            // 
            // numTauxTVA
            // 
            this.numTauxTVA.DecimalPlaces = 2;
            this.numTauxTVA.Location = new System.Drawing.Point(420, 325);
            this.numTauxTVA.Maximum = new decimal(new int[] { 100, 0, 0, 0 });
            this.numTauxTVA.Name = "numTauxTVA";
            this.numTauxTVA.Size = new System.Drawing.Size(100, 22);
            this.numTauxTVA.TabIndex = 21;
            // 
            // chkActif
            // 
            this.chkActif.Checked = true;
            this.chkActif.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkActif.Location = new System.Drawing.Point(20, 370);
            this.chkActif.Name = "chkActif";
            this.chkActif.Size = new System.Drawing.Size(50, 20);
            this.chkActif.TabIndex = 22;
            this.chkActif.Values.Text = "Actif";
            // 
            // panelInfo
            // 
            this.panelInfo.Controls.Add(this.lblInfoModification);
            this.panelInfo.Controls.Add(this.lblInfoCreation);
            this.panelInfo.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.panelInfo.Location = new System.Drawing.Point(10, 480);
            this.panelInfo.Name = "panelInfo";
            this.panelInfo.Size = new System.Drawing.Size(780, 60);
            this.panelInfo.TabIndex = 1;
            // 
            // lblInfoCreation
            // 
            this.lblInfoCreation.Location = new System.Drawing.Point(10, 10);
            this.lblInfoCreation.Name = "lblInfoCreation";
            this.lblInfoCreation.Size = new System.Drawing.Size(6, 2);
            this.lblInfoCreation.StateCommon.ShortText.Color1 = System.Drawing.Color.Gray;
            this.lblInfoCreation.StateCommon.ShortText.Font = new System.Drawing.Font("Segoe UI", 8F);
            this.lblInfoCreation.TabIndex = 0;
            this.lblInfoCreation.Values.Text = "";
            this.lblInfoCreation.Visible = false;
            // 
            // lblInfoModification
            // 
            this.lblInfoModification.Location = new System.Drawing.Point(10, 30);
            this.lblInfoModification.Name = "lblInfoModification";
            this.lblInfoModification.Size = new System.Drawing.Size(6, 2);
            this.lblInfoModification.StateCommon.ShortText.Color1 = System.Drawing.Color.Gray;
            this.lblInfoModification.StateCommon.ShortText.Font = new System.Drawing.Font("Segoe UI", 8F);
            this.lblInfoModification.TabIndex = 1;
            this.lblInfoModification.Values.Text = "";
            this.lblInfoModification.Visible = false;
            // 
            // panelBoutons
            // 
            this.panelBoutons.Controls.Add(this.btnAnnuler);
            this.panelBoutons.Controls.Add(this.btnEnregistrer);
            this.panelBoutons.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.panelBoutons.Location = new System.Drawing.Point(10, 540);
            this.panelBoutons.Name = "panelBoutons";
            this.panelBoutons.Size = new System.Drawing.Size(780, 50);
            this.panelBoutons.TabIndex = 2;
            // 
            // btnEnregistrer
            // 
            this.btnEnregistrer.Location = new System.Drawing.Point(560, 10);
            this.btnEnregistrer.Name = "btnEnregistrer";
            this.btnEnregistrer.Size = new System.Drawing.Size(100, 32);
            this.btnEnregistrer.StateCommon.Back.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(122)))), ((int)(((byte)(204)))));
            this.btnEnregistrer.StateCommon.Back.Color2 = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(122)))), ((int)(((byte)(204)))));
            this.btnEnregistrer.StateCommon.Content.ShortText.Color1 = System.Drawing.Color.White;
            this.btnEnregistrer.TabIndex = 0;
            this.btnEnregistrer.Values.Text = "💾 Enregistrer";
            this.btnEnregistrer.Click += new System.EventHandler(this.btnEnregistrer_Click);
            // 
            // btnAnnuler
            // 
            this.btnAnnuler.Location = new System.Drawing.Point(670, 10);
            this.btnAnnuler.Name = "btnAnnuler";
            this.btnAnnuler.Size = new System.Drawing.Size(100, 32);
            this.btnAnnuler.TabIndex = 1;
            this.btnAnnuler.Values.Text = "❌ Annuler";
            this.btnAnnuler.Click += new System.EventHandler(this.btnAnnuler_Click);
            // 
            // FRM_PRODUIT_EDIT
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(800, 600);
            this.Controls.Add(this.panelMain);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
            this.KeyPreview = true;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "FRM_PRODUIT_EDIT";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.Text = "Produit";
            this.Load += new System.EventHandler(this.FRM_PRODUIT_EDIT_Load);
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.FRM_PRODUIT_EDIT_FormClosing);
            this.KeyDown += new System.Windows.Forms.KeyEventHandler(this.FRM_PRODUIT_EDIT_KeyDown);
            ((System.ComponentModel.ISupportInitialize)(this.panelMain)).EndInit();
            this.panelMain.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupBoxDetails.Panel)).EndInit();
            this.groupBoxDetails.Panel.ResumeLayout(false);
            this.groupBoxDetails.Panel.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupBoxDetails)).EndInit();
            this.groupBoxDetails.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.cmbCategorie)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cmbUnite)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelInfo)).EndInit();
            this.panelInfo.ResumeLayout(false);
            this.panelInfo.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.panelBoutons)).EndInit();
            this.panelBoutons.ResumeLayout(false);
            this.ResumeLayout(false);
        }

        #endregion

        private Krypton.Toolkit.KryptonPanel panelMain;
        private Krypton.Toolkit.KryptonGroupBox groupBoxDetails;
        private Krypton.Toolkit.KryptonLabel lblCodeBarre;
        private Krypton.Toolkit.KryptonTextBox txtCodeBarre;
        private Krypton.Toolkit.KryptonLabel lblCodeInterne;
        private Krypton.Toolkit.KryptonTextBox txtCodeInterne;
        private Krypton.Toolkit.KryptonLabel lblNom;
        private Krypton.Toolkit.KryptonTextBox txtNom;
        private Krypton.Toolkit.KryptonLabel lblDescription;
        private Krypton.Toolkit.KryptonTextBox txtDescription;
        private Krypton.Toolkit.KryptonLabel lblMarque;
        private Krypton.Toolkit.KryptonTextBox txtMarque;
        private Krypton.Toolkit.KryptonLabel lblCategorie;
        private Krypton.Toolkit.KryptonComboBox cmbCategorie;
        private Krypton.Toolkit.KryptonLabel lblUnite;
        private Krypton.Toolkit.KryptonComboBox cmbUnite;
        private Krypton.Toolkit.KryptonLabel lblPrixAchat;
        private Krypton.Toolkit.KryptonNumericUpDown numPrixAchat;
        private Krypton.Toolkit.KryptonLabel lblPrixVente;
        private Krypton.Toolkit.KryptonNumericUpDown numPrixVente;
        private Krypton.Toolkit.KryptonLabel lblStockMinimum;
        private Krypton.Toolkit.KryptonNumericUpDown numStockMinimum;
        private Krypton.Toolkit.KryptonLabel lblTauxTVA;
        private Krypton.Toolkit.KryptonNumericUpDown numTauxTVA;
        private Krypton.Toolkit.KryptonCheckBox chkActif;
        private Krypton.Toolkit.KryptonPanel panelInfo;
        private Krypton.Toolkit.KryptonLabel lblInfoCreation;
        private Krypton.Toolkit.KryptonLabel lblInfoModification;
        private Krypton.Toolkit.KryptonPanel panelBoutons;
        private Krypton.Toolkit.KryptonButton btnEnregistrer;
        private Krypton.Toolkit.KryptonButton btnAnnuler;
    }
}

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using Krypton.Toolkit;
using Super_Market.Business.Services;
using Super_Market.Core.Models;

namespace Super_Market.App.Forms
{
    public partial class FRM_PRODUIT_EDIT : KryptonForm
    {
        private readonly ProduitService _produitService;
        private readonly CategorieService _categorieService;
        private readonly Utilisateur _utilisateurConnecte;
        private readonly Produit _produitAModifier;
        private readonly bool _modeConsultation;
        private bool _estNouveau;

        public FRM_PRODUIT_EDIT(Utilisateur utilisateur, Produit produit = null, bool modeConsultation = false)
        {
            InitializeComponent();
            _produitService = new ProduitService();
            _categorieService = new CategorieService();
            _utilisateurConnecte = utilisateur;
            _produitAModifier = produit;
            _modeConsultation = modeConsultation;
            _estNouveau = produit == null;
            
            InitialiserFormulaire();
        }

        private void InitialiserFormulaire()
        {
            if (_modeConsultation)
            {
                this.Text = "Consultation Produit";
                btnEnregistrer.Visible = false;
                btnAnnuler.Text = "Fermer";
            }
            else if (_estNouveau)
            {
                this.Text = "Nouveau Produit";
            }
            else
            {
                this.Text = "Modifier Produit";
            }

            this.Size = new System.Drawing.Size(800, 600);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;

            // Charger les données de référence
            ChargerCategories();
            ChargerUnites();

            // Charger les données si modification/consultation
            if (!_estNouveau)
            {
                ChargerDonneesProduit();
            }
            else
            {
                // Valeurs par défaut pour un nouveau produit
                chkActif.Checked = true;
                numPrixAchat.Value = 0;
                numPrixVente.Value = 0;
                numStockMinimum.Value = 10;
                numTauxTVA.Value = 19; // TVA par défaut
            }

            // Configurer les contrôles selon le mode
            ConfigurerControles();
        }

        private async void ChargerCategories()
        {
            try
            {
                var categories = await _categorieService.GetAllCategoriesAsync();
                
                cmbCategorie.Items.Clear();
                cmbCategorie.Items.Add(new { Id = 0, Nom = "Sélectionner une catégorie..." });
                
                foreach (var categorie in categories.Where(c => c.EstActif))
                {
                    cmbCategorie.Items.Add(new { Id = categorie.Id, Nom = categorie.Nom });
                }
                
                cmbCategorie.DisplayMember = "Nom";
                cmbCategorie.ValueMember = "Id";
                cmbCategorie.SelectedIndex = 0;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors du chargement des catégories: {ex.Message}",
                              "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ChargerUnites()
        {
            // Unités de mesure courantes
            cmbUnite.Items.Clear();
            cmbUnite.Items.AddRange(new string[] {
                "pièce", "kg", "g", "l", "ml", "m", "cm", "m²", "m³", 
                "boîte", "paquet", "carton", "lot", "douzaine"
            });
            cmbUnite.Text = "pièce";
        }

        private void ChargerDonneesProduit()
        {
            if (_produitAModifier != null)
            {
                txtCodeBarre.Text = _produitAModifier.CodeBarre ?? "";
                txtCodeInterne.Text = _produitAModifier.CodeInterne ?? "";
                txtNom.Text = _produitAModifier.Nom ?? "";
                txtDescription.Text = _produitAModifier.Description ?? "";
                txtMarque.Text = _produitAModifier.Marque ?? "";
                cmbUnite.Text = _produitAModifier.Unite ?? "pièce";
                numPrixAchat.Value = _produitAModifier.PrixAchat;
                numPrixVente.Value = _produitAModifier.PrixVente;
                numStockMinimum.Value = _produitAModifier.StockMinimum;
                numTauxTVA.Value = _produitAModifier.TauxTVA;
                chkActif.Checked = _produitAModifier.EstActif;
                
                // Sélectionner la catégorie
                for (int i = 0; i < cmbCategorie.Items.Count; i++)
                {
                    var item = (dynamic)cmbCategorie.Items[i];
                    if (item.Id == _produitAModifier.CategorieId)
                    {
                        cmbCategorie.SelectedIndex = i;
                        break;
                    }
                }

                // Afficher les informations de création/modification
                if (_produitAModifier.DateCreation != default(DateTime))
                {
                    lblInfoCreation.Text = $"Créé le {_produitAModifier.DateCreation:dd/MM/yyyy HH:mm} par {_produitAModifier.UtilisateurCreation}";
                    lblInfoCreation.Visible = true;
                }

                if (_produitAModifier.DateModification.HasValue)
                {
                    lblInfoModification.Text = $"Modifié le {_produitAModifier.DateModification:dd/MM/yyyy HH:mm} par {_produitAModifier.UtilisateurModification}";
                    lblInfoModification.Visible = true;
                }
            }
        }

        private void ConfigurerControles()
        {
            bool enEdition = !_modeConsultation;
            
            txtCodeBarre.ReadOnly = !enEdition;
            txtCodeInterne.ReadOnly = !enEdition;
            txtNom.ReadOnly = !enEdition;
            txtDescription.ReadOnly = !enEdition;
            txtMarque.ReadOnly = !enEdition;
            cmbCategorie.Enabled = enEdition;
            cmbUnite.Enabled = enEdition;
            numPrixAchat.Enabled = enEdition;
            numPrixVente.Enabled = enEdition;
            numStockMinimum.Enabled = enEdition;
            numTauxTVA.Enabled = enEdition;
            chkActif.Enabled = enEdition;
            
            if (enEdition)
            {
                txtNom.Focus();
            }
        }

        private async void btnEnregistrer_Click(object sender, EventArgs e)
        {
            await EnregistrerProduit();
        }

        private async Task EnregistrerProduit()
        {
            try
            {
                // Validation
                if (string.IsNullOrWhiteSpace(txtNom.Text))
                {
                    MessageBox.Show("Le nom du produit est obligatoire.",
                                  "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtNom.Focus();
                    return;
                }

                var categorieItem = (dynamic)cmbCategorie.SelectedItem;
                if (categorieItem?.Id == 0)
                {
                    MessageBox.Show("Veuillez sélectionner une catégorie.",
                                  "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    cmbCategorie.Focus();
                    return;
                }

                if (numPrixVente.Value <= 0)
                {
                    MessageBox.Show("Le prix de vente doit être supérieur à 0.",
                                  "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    numPrixVente.Focus();
                    return;
                }

                // Désactiver les contrôles pendant l'enregistrement
                btnEnregistrer.Enabled = false;
                btnAnnuler.Enabled = false;
                this.Cursor = Cursors.WaitCursor;

                var produit = _produitAModifier ?? new Produit();
                
                produit.CodeBarre = txtCodeBarre.Text.Trim();
                produit.CodeInterne = txtCodeInterne.Text.Trim();
                produit.Nom = txtNom.Text.Trim();
                produit.Description = txtDescription.Text.Trim();
                produit.Marque = txtMarque.Text.Trim();
                produit.CategorieId = categorieItem.Id;
                produit.Unite = cmbUnite.Text;
                produit.PrixAchat = numPrixAchat.Value;
                produit.PrixVente = numPrixVente.Value;
                produit.StockMinimum = (int)numStockMinimum.Value;
                produit.TauxTVA = numTauxTVA.Value;
                produit.EstActif = chkActif.Checked;
                
                if (_estNouveau)
                {
                    // Nouveau
                    produit.DateCreation = DateTime.Now;
                    produit.UtilisateurCreation = _utilisateurConnecte.NomUtilisateur;
                    
                    await _produitService.AjouterProduitAsync(produit);
                    
                    MessageBox.Show("Produit ajouté avec succès !",
                                  "Succès", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    // Modification
                    produit.DateModification = DateTime.Now;
                    produit.UtilisateurModification = _utilisateurConnecte.NomUtilisateur;
                    
                    await _produitService.ModifierProduitAsync(produit);
                    
                    MessageBox.Show("Produit modifié avec succès !",
                                  "Succès", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }

                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors de l'enregistrement: {ex.Message}",
                              "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                // Réactiver les contrôles
                btnEnregistrer.Enabled = true;
                btnAnnuler.Enabled = true;
                this.Cursor = Cursors.Default;
            }
        }

        private void btnAnnuler_Click(object sender, EventArgs e)
        {
            if (_modeConsultation)
            {
                this.DialogResult = DialogResult.Cancel;
                this.Close();
                return;
            }

            // Vérifier s'il y a des modifications non sauvegardées
            if (YaDesModifications())
            {
                var result = MessageBox.Show(
                    "Des modifications ont été apportées. Voulez-vous les abandonner ?",
                    "Modifications non sauvegardées",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result == DialogResult.No)
                {
                    return;
                }
            }

            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private bool YaDesModifications()
        {
            if (_estNouveau)
            {
                return !string.IsNullOrWhiteSpace(txtNom.Text) ||
                       !string.IsNullOrWhiteSpace(txtDescription.Text) ||
                       numPrixVente.Value > 0;
            }
            else if (_produitAModifier != null)
            {
                var categorieItem = (dynamic)cmbCategorie.SelectedItem;
                return txtCodeBarre.Text.Trim() != (_produitAModifier.CodeBarre ?? "") ||
                       txtNom.Text.Trim() != (_produitAModifier.Nom ?? "") ||
                       txtDescription.Text.Trim() != (_produitAModifier.Description ?? "") ||
                       categorieItem?.Id != _produitAModifier.CategorieId ||
                       numPrixVente.Value != _produitAModifier.PrixVente ||
                       chkActif.Checked != _produitAModifier.EstActif;
            }

            return false;
        }

        private void FRM_PRODUIT_EDIT_Load(object sender, EventArgs e)
        {
            if (!_modeConsultation && _estNouveau)
            {
                txtNom.Focus();
            }
        }

        private void FRM_PRODUIT_EDIT_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (!_modeConsultation && e.CloseReason == CloseReason.UserClosing)
            {
                if (YaDesModifications())
                {
                    var result = MessageBox.Show(
                        "Des modifications ont été apportées. Voulez-vous les abandonner ?",
                        "Modifications non sauvegardées",
                        MessageBoxButtons.YesNo,
                        MessageBoxIcon.Question);

                    if (result == DialogResult.No)
                    {
                        e.Cancel = true;
                    }
                }
            }
        }

        private void numPrixAchat_ValueChanged(object sender, EventArgs e)
        {
            // Calculer automatiquement le prix de vente avec une marge de 30%
            if (numPrixAchat.Value > 0 && numPrixVente.Value == 0)
            {
                numPrixVente.Value = numPrixAchat.Value * 1.3m;
            }
        }

        private void FRM_PRODUIT_EDIT_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Escape)
            {
                btnAnnuler_Click(sender, e);
            }
            else if (e.KeyCode == Keys.F2 && !_modeConsultation)
            {
                btnEnregistrer_Click(sender, e);
            }
        }
    }
}

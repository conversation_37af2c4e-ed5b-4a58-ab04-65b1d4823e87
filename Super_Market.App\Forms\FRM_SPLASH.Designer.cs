namespace Super_Market.App.Forms
{
    partial class FRM_SPLASH
    {
        private System.ComponentModel.IContainer components = null;

        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        private void InitializeComponent()
        {
            this.panelMain = new Krypton.Toolkit.KryptonPanel();
            this.pictureBoxLogo = new System.Windows.Forms.PictureBox();
            this.lblTitle = new Krypton.Toolkit.KryptonLabel();
            this.lblSubtitle = new Krypton.Toolkit.KryptonLabel();
            this.lblVersion = new Krypton.Toolkit.KryptonLabel();
            this.lblCopyright = new Krypton.Toolkit.KryptonLabel();
            this.lblStatus = new Krypton.Toolkit.KryptonLabel();
            this.progressBar = new System.Windows.Forms.ProgressBar();
            ((System.ComponentModel.ISupportInitialize)(this.panelMain)).BeginInit();
            this.panelMain.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBoxLogo)).BeginInit();
            this.SuspendLayout();
            // 
            // panelMain
            // 
            this.panelMain.Controls.Add(this.progressBar);
            this.panelMain.Controls.Add(this.lblStatus);
            this.panelMain.Controls.Add(this.lblCopyright);
            this.panelMain.Controls.Add(this.lblVersion);
            this.panelMain.Controls.Add(this.lblSubtitle);
            this.panelMain.Controls.Add(this.lblTitle);
            this.panelMain.Controls.Add(this.pictureBoxLogo);
            this.panelMain.Dock = System.Windows.Forms.DockStyle.Fill;
            this.panelMain.Location = new System.Drawing.Point(0, 0);
            this.panelMain.Name = "panelMain";
            this.panelMain.Size = new System.Drawing.Size(600, 400);
            this.panelMain.StateCommon.Color1 = System.Drawing.Color.White;
            this.panelMain.StateCommon.Color2 = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(240)))), ((int)(((byte)(240)))));
            this.panelMain.StateCommon.ColorAngle = 90F;
            this.panelMain.TabIndex = 0;
            // 
            // pictureBoxLogo
            // 
            this.pictureBoxLogo.BackColor = System.Drawing.Color.Transparent;
            this.pictureBoxLogo.Location = new System.Drawing.Point(250, 50);
            this.pictureBoxLogo.Name = "pictureBoxLogo";
            this.pictureBoxLogo.Size = new System.Drawing.Size(100, 80);
            this.pictureBoxLogo.SizeMode = System.Windows.Forms.PictureBoxSizeMode.Zoom;
            this.pictureBoxLogo.TabIndex = 0;
            this.pictureBoxLogo.TabStop = false;
            // 
            // lblTitle
            // 
            this.lblTitle.Location = new System.Drawing.Point(200, 140);
            this.lblTitle.Name = "lblTitle";
            this.lblTitle.Size = new System.Drawing.Size(200, 40);
            this.lblTitle.StateCommon.ShortText.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(122)))), ((int)(((byte)(204)))));
            this.lblTitle.StateCommon.ShortText.Font = new System.Drawing.Font("Segoe UI", 24F, System.Drawing.FontStyle.Bold);
            this.lblTitle.StateCommon.ShortText.TextH = Krypton.Toolkit.PaletteRelativeAlign.Center;
            this.lblTitle.TabIndex = 1;
            this.lblTitle.Values.Text = "ZinStore";
            // 
            // lblSubtitle
            // 
            this.lblSubtitle.Location = new System.Drawing.Point(150, 185);
            this.lblSubtitle.Name = "lblSubtitle";
            this.lblSubtitle.Size = new System.Drawing.Size(300, 25);
            this.lblSubtitle.StateCommon.ShortText.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.lblSubtitle.StateCommon.ShortText.Font = new System.Drawing.Font("Segoe UI", 12F);
            this.lblSubtitle.StateCommon.ShortText.TextH = Krypton.Toolkit.PaletteRelativeAlign.Center;
            this.lblSubtitle.TabIndex = 2;
            this.lblSubtitle.Values.Text = "Système de Gestion de Supermarché";
            // 
            // lblVersion
            // 
            this.lblVersion.Location = new System.Drawing.Point(250, 220);
            this.lblVersion.Name = "lblVersion";
            this.lblVersion.Size = new System.Drawing.Size(100, 20);
            this.lblVersion.StateCommon.ShortText.Color1 = System.Drawing.Color.Gray;
            this.lblVersion.StateCommon.ShortText.Font = new System.Drawing.Font("Segoe UI", 9F);
            this.lblVersion.StateCommon.ShortText.TextH = Krypton.Toolkit.PaletteRelativeAlign.Center;
            this.lblVersion.TabIndex = 3;
            this.lblVersion.Values.Text = "Version 1.0.0";
            // 
            // lblCopyright
            // 
            this.lblCopyright.Location = new System.Drawing.Point(150, 360);
            this.lblCopyright.Name = "lblCopyright";
            this.lblCopyright.Size = new System.Drawing.Size(300, 20);
            this.lblCopyright.StateCommon.ShortText.Color1 = System.Drawing.Color.Gray;
            this.lblCopyright.StateCommon.ShortText.Font = new System.Drawing.Font("Segoe UI", 8F);
            this.lblCopyright.StateCommon.ShortText.TextH = Krypton.Toolkit.PaletteRelativeAlign.Center;
            this.lblCopyright.TabIndex = 4;
            this.lblCopyright.Values.Text = "© 2025 ZinStore. Tous droits réservés.";
            // 
            // lblStatus
            // 
            this.lblStatus.Location = new System.Drawing.Point(50, 280);
            this.lblStatus.Name = "lblStatus";
            this.lblStatus.Size = new System.Drawing.Size(500, 20);
            this.lblStatus.StateCommon.ShortText.Color1 = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(122)))), ((int)(((byte)(204)))));
            this.lblStatus.StateCommon.ShortText.Font = new System.Drawing.Font("Segoe UI", 9F);
            this.lblStatus.StateCommon.ShortText.TextH = Krypton.Toolkit.PaletteRelativeAlign.Center;
            this.lblStatus.TabIndex = 5;
            this.lblStatus.Values.Text = "Initialisation de l'application...";
            // 
            // progressBar
            // 
            this.progressBar.Location = new System.Drawing.Point(100, 310);
            this.progressBar.Name = "progressBar";
            this.progressBar.Size = new System.Drawing.Size(400, 20);
            this.progressBar.Style = System.Windows.Forms.ProgressBarStyle.Continuous;
            this.progressBar.TabIndex = 6;
            // 
            // FRM_SPLASH
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(600, 400);
            this.Controls.Add(this.panelMain);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.None;
            this.Name = "FRM_SPLASH";
            this.ShowIcon = false;
            this.ShowInTaskbar = false;
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "ZinStore";
            this.TopMost = true;
            this.Load += new System.EventHandler(this.FRM_SPLASH_Load);
            this.Paint += new System.Windows.Forms.PaintEventHandler(this.FRM_SPLASH_Paint);
            ((System.ComponentModel.ISupportInitialize)(this.panelMain)).EndInit();
            this.panelMain.ResumeLayout(false);
            this.panelMain.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBoxLogo)).EndInit();
            this.ResumeLayout(false);
        }

        #endregion

        private Krypton.Toolkit.KryptonPanel panelMain;
        private System.Windows.Forms.PictureBox pictureBoxLogo;
        private Krypton.Toolkit.KryptonLabel lblTitle;
        private Krypton.Toolkit.KryptonLabel lblSubtitle;
        private Krypton.Toolkit.KryptonLabel lblVersion;
        private Krypton.Toolkit.KryptonLabel lblCopyright;
        private Krypton.Toolkit.KryptonLabel lblStatus;
        private System.Windows.Forms.ProgressBar progressBar;
    }
}

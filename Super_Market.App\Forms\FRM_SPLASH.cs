using System;
using System.Drawing;
using System.Threading.Tasks;
using System.Windows.Forms;
using Krypton.Toolkit;

namespace Super_Market.App.Forms
{
    public partial class FRM_SPLASH : KryptonForm
    {
        private Timer _timer;
        private int _progressValue = 0;
        private string[] _loadingMessages = {
            "Initialisation de l'application...",
            "Chargement des modules...",
            "Connexion à la base de données...",
            "Vérification des permissions...",
            "Chargement de l'interface...",
            "Finalisation..."
        };
        private int _currentMessageIndex = 0;

        public FRM_SPLASH()
        {
            InitializeComponent();
            InitialiserSplashScreen();
        }

        private void InitialiserSplashScreen()
        {
            // Configuration du formulaire
            this.FormBorderStyle = FormBorderStyle.None;
            this.StartPosition = FormStartPosition.CenterScreen;
            this.Size = new Size(600, 400);
            this.BackColor = Color.White;
            this.ShowInTaskbar = false;
            this.TopMost = true;

            // Initialiser les contrôles
            lblVersion.Text = "Version 1.0.0";
            lblCopyright.Text = "© 2025 ZinStore. Tous droits réservés.";
            lblStatus.Text = _loadingMessages[0];
            progressBar.Value = 0;
            progressBar.Maximum = 100;

            // Configurer le timer
            _timer = new Timer();
            _timer.Interval = 100; // 100ms
            _timer.Tick += Timer_Tick;
        }

        private void FRM_SPLASH_Load(object sender, EventArgs e)
        {
            // Démarrer l'animation de chargement
            _timer.Start();
        }

        private void Timer_Tick(object sender, EventArgs e)
        {
            _progressValue += 2;
            progressBar.Value = Math.Min(_progressValue, 100);

            // Changer le message de statut
            if (_progressValue % 17 == 0 && _currentMessageIndex < _loadingMessages.Length - 1)
            {
                _currentMessageIndex++;
                lblStatus.Text = _loadingMessages[_currentMessageIndex];
            }

            // Quand le chargement est terminé
            if (_progressValue >= 100)
            {
                _timer.Stop();
                lblStatus.Text = "Chargement terminé !";
                
                // Attendre un peu puis fermer
                Task.Delay(500).ContinueWith(t =>
                {
                    this.Invoke(new Action(() =>
                    {
                        this.DialogResult = DialogResult.OK;
                        this.Close();
                    }));
                });
            }
        }

        private void FRM_SPLASH_Paint(object sender, PaintEventArgs e)
        {
            // Dessiner une bordure
            using (var pen = new Pen(Color.FromArgb(0, 122, 204), 2))
            {
                e.Graphics.DrawRectangle(pen, 0, 0, this.Width - 1, this.Height - 1);
            }
        }

        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            _timer?.Stop();
            _timer?.Dispose();
            base.OnFormClosing(e);
        }

        // Empêcher la fermeture par Alt+F4
        protected override bool ProcessCmdKey(ref Message msg, Keys keyData)
        {
            if (keyData == (Keys.Alt | Keys.F4))
            {
                return true;
            }
            return base.ProcessCmdKey(ref msg, keyData);
        }
    }
}

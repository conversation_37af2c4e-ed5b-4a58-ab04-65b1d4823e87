using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Forms;
using Krypton.Toolkit;
using Super_Market.Core.Models;

namespace Super_Market.App.Forms
{
    public partial class FRM_UNITES : KryptonForm
    {
        private readonly Utilisateur _utilisateurConnecte;
        private List<Unite> _unites;
        private Unite _uniteSelectionnee;
        private bool _modeEdition = false;

        public FRM_UNITES(Utilisateur utilisateur)
        {
            InitializeComponent();
            _utilisateurConnecte = utilisateur;
            InitialiserFormulaire();
        }

        private void InitialiserFormulaire()
        {
            this.Text = "Gestion des Unités de Mesure";
            this.Size = new System.Drawing.Size(800, 600);
            this.StartPosition = FormStartPosition.CenterParent;
            
            // Configurer la grille
            ConfigurerGrille();
            
            // Charger les données
            ChargerUnites();
            
            // État initial des boutons
            ActiverBoutons(false);
            btnAjouter.Enabled = true;
            btnActualiser.Enabled = true;
        }

        private void ConfigurerGrille()
        {
            dgvUnites.AutoGenerateColumns = false;
            dgvUnites.AllowUserToAddRows = false;
            dgvUnites.AllowUserToDeleteRows = false;
            dgvUnites.ReadOnly = true;
            dgvUnites.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvUnites.MultiSelect = false;

            dgvUnites.Columns.Clear();
            
            dgvUnites.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Code",
                HeaderText = "Code",
                DataPropertyName = "Code",
                Width = 80
            });

            dgvUnites.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Nom",
                HeaderText = "Nom de l'Unité",
                DataPropertyName = "Nom",
                Width = 150
            });

            dgvUnites.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Symbole",
                HeaderText = "Symbole",
                DataPropertyName = "Symbole",
                Width = 80
            });

            dgvUnites.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Description",
                HeaderText = "Description",
                DataPropertyName = "Description",
                Width = 200
            });

            dgvUnites.Columns.Add(new DataGridViewCheckBoxColumn
            {
                Name = "EstActif",
                HeaderText = "Actif",
                DataPropertyName = "EstActif",
                Width = 60
            });
        }

        private void ChargerUnites()
        {
            try
            {
                lblStatus.Text = "Chargement des unités...";
                
                // Données d'exemple - remplacer par un service réel
                _unites = new List<Unite>
                {
                    new Unite { Id = 1, Code = "PC", Nom = "Pièce", Symbole = "pc", Description = "Unité de comptage", EstActif = true },
                    new Unite { Id = 2, Code = "KG", Nom = "Kilogramme", Symbole = "kg", Description = "Unité de poids", EstActif = true },
                    new Unite { Id = 3, Code = "L", Nom = "Litre", Symbole = "l", Description = "Unité de volume", EstActif = true },
                    new Unite { Id = 4, Code = "M", Nom = "Mètre", Symbole = "m", Description = "Unité de longueur", EstActif = true },
                    new Unite { Id = 5, Code = "BOX", Nom = "Boîte", Symbole = "box", Description = "Emballage", EstActif = true },
                    new Unite { Id = 6, Code = "PAQ", Nom = "Paquet", Symbole = "paq", Description = "Emballage", EstActif = true }
                };
                
                dgvUnites.DataSource = _unites;
                
                lblStatus.Text = $"{_unites.Count} unité(s) chargée(s)";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors du chargement des unités: {ex.Message}",
                              "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                lblStatus.Text = "Erreur de chargement";
            }
        }

        private void dgvUnites_SelectionChanged(object sender, EventArgs e)
        {
            if (dgvUnites.SelectedRows.Count > 0)
            {
                var index = dgvUnites.SelectedRows[0].Index;
                if (index >= 0 && index < _unites.Count)
                {
                    _uniteSelectionnee = _unites[index];
                    AfficherUnite(_uniteSelectionnee);
                    ActiverBoutons(true);
                }
            }
            else
            {
                _uniteSelectionnee = null;
                ViderChamps();
                ActiverBoutons(false);
            }
        }

        private void AfficherUnite(Unite unite)
        {
            if (unite != null)
            {
                txtCode.Text = unite.Code ?? "";
                txtNom.Text = unite.Nom ?? "";
                txtSymbole.Text = unite.Symbole ?? "";
                txtDescription.Text = unite.Description ?? "";
                chkActif.Checked = unite.EstActif;
            }
        }

        private void ViderChamps()
        {
            txtCode.Text = "";
            txtNom.Text = "";
            txtSymbole.Text = "";
            txtDescription.Text = "";
            chkActif.Checked = true;
        }

        private void ActiverBoutons(bool uniteSelectionnee)
        {
            btnModifier.Enabled = uniteSelectionnee && !_modeEdition;
            btnSupprimer.Enabled = uniteSelectionnee && !_modeEdition;
            btnEnregistrer.Enabled = _modeEdition;
            btnAnnuler.Enabled = _modeEdition;
        }

        private void ActiverModeEdition(bool activer)
        {
            _modeEdition = activer;
            
            txtCode.ReadOnly = !activer;
            txtNom.ReadOnly = !activer;
            txtSymbole.ReadOnly = !activer;
            txtDescription.ReadOnly = !activer;
            chkActif.Enabled = activer;
            
            dgvUnites.Enabled = !activer;
            
            ActiverBoutons(_uniteSelectionnee != null);
        }

        private void btnAjouter_Click(object sender, EventArgs e)
        {
            _uniteSelectionnee = null;
            ViderChamps();
            ActiverModeEdition(true);
            txtCode.Focus();
        }

        private void btnModifier_Click(object sender, EventArgs e)
        {
            if (_uniteSelectionnee != null)
            {
                ActiverModeEdition(true);
                txtNom.Focus();
            }
        }

        private void btnEnregistrer_Click(object sender, EventArgs e)
        {
            EnregistrerUnite();
        }

        private void EnregistrerUnite()
        {
            try
            {
                // Validation
                if (string.IsNullOrWhiteSpace(txtCode.Text))
                {
                    MessageBox.Show("Le code de l'unité est obligatoire.",
                                  "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtCode.Focus();
                    return;
                }

                if (string.IsNullOrWhiteSpace(txtNom.Text))
                {
                    MessageBox.Show("Le nom de l'unité est obligatoire.",
                                  "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtNom.Focus();
                    return;
                }

                // Vérifier l'unicité du code
                var codeExiste = _unites.Any(u => u.Code.Equals(txtCode.Text.Trim(), StringComparison.OrdinalIgnoreCase) 
                                                 && (_uniteSelectionnee == null || u.Id != _uniteSelectionnee.Id));
                if (codeExiste)
                {
                    MessageBox.Show("Ce code d'unité existe déjà.",
                                  "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtCode.Focus();
                    return;
                }

                var unite = _uniteSelectionnee ?? new Unite();
                
                unite.Code = txtCode.Text.Trim().ToUpper();
                unite.Nom = txtNom.Text.Trim();
                unite.Symbole = txtSymbole.Text.Trim();
                unite.Description = txtDescription.Text.Trim();
                unite.EstActif = chkActif.Checked;
                
                if (_uniteSelectionnee == null)
                {
                    // Nouveau
                    unite.Id = _unites.Count > 0 ? _unites.Max(u => u.Id) + 1 : 1;
                    _unites.Add(unite);
                    
                    MessageBox.Show("Unité ajoutée avec succès !",
                                  "Succès", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    // Modification
                    MessageBox.Show("Unité modifiée avec succès !",
                                  "Succès", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }

                ActiverModeEdition(false);
                ChargerUnites();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors de l'enregistrement: {ex.Message}",
                              "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnAnnuler_Click(object sender, EventArgs e)
        {
            ActiverModeEdition(false);
            if (_uniteSelectionnee != null)
            {
                AfficherUnite(_uniteSelectionnee);
            }
            else
            {
                ViderChamps();
            }
        }

        private void btnSupprimer_Click(object sender, EventArgs e)
        {
            if (_uniteSelectionnee != null)
            {
                var result = MessageBox.Show(
                    $"Êtes-vous sûr de vouloir supprimer l'unité '{_uniteSelectionnee.Nom}' ?",
                    "Confirmation de suppression",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    try
                    {
                        _unites.Remove(_uniteSelectionnee);
                        MessageBox.Show("Unité supprimée avec succès !",
                                      "Succès", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        ChargerUnites();
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"Erreur lors de la suppression: {ex.Message}",
                                      "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
        }

        private void btnActualiser_Click(object sender, EventArgs e)
        {
            ChargerUnites();
        }

        private void btnFermer_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void txtRecherche_TextChanged(object sender, EventArgs e)
        {
            FiltrerUnites();
        }

        private void FiltrerUnites()
        {
            if (_unites == null) return;

            var termeRecherche = txtRecherche.Text.ToLower();
            
            if (string.IsNullOrWhiteSpace(termeRecherche))
            {
                dgvUnites.DataSource = _unites;
            }
            else
            {
                var unitesFiltrees = _unites.Where(u =>
                    (u.Code?.ToLower().Contains(termeRecherche) ?? false) ||
                    (u.Nom?.ToLower().Contains(termeRecherche) ?? false) ||
                    (u.Symbole?.ToLower().Contains(termeRecherche) ?? false) ||
                    (u.Description?.ToLower().Contains(termeRecherche) ?? false)
                ).ToList();
                
                dgvUnites.DataSource = unitesFiltrees;
            }
        }

        private void FRM_UNITES_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (_modeEdition)
            {
                var result = MessageBox.Show(
                    "Des modifications sont en cours. Voulez-vous les abandonner ?",
                    "Modifications en cours",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result == DialogResult.No)
                {
                    e.Cancel = true;
                }
            }
        }
    }

    // Classe Unite simple pour la gestion des unités
    public class Unite
    {
        public int Id { get; set; }
        public string Code { get; set; }
        public string Nom { get; set; }
        public string Symbole { get; set; }
        public string Description { get; set; }
        public bool EstActif { get; set; } = true;
    }
}

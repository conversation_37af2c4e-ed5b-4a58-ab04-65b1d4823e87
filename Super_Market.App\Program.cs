﻿using System;
using System.Windows.Forms;
using Super_Market.App.Forms;

namespace Super_Market.App
{
    internal static class Program
    {
        /// <summary>
        /// Point d'entrée principal de l'application ZinStore - Gestion de Supermarché
        /// </summary>
        [STAThread]
        static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);

            // Configurer la chaîne de connexion à la base de données
            Super_Market.Data.DatabaseConfig.ConnectionString =
                "Server=localhost;Database=zinstore_db;Uid=root;Pwd=;CharSet=utf8;";

            // Afficher le splash screen
            using (var splash = new FRM_SPLASH())
            {
                if (splash.ShowDialog() == DialogResult.OK)
                {
                    // Démarrer avec le formulaire de connexion
                    Application.Run(new FRM_LOGIN());
                }
            }
        }
    }
}

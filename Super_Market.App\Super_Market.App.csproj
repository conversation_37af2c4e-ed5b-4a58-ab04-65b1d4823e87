<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{C004CE99-F3F7-4F33-B14C-D80A1B376859}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <RootNamespace>Super_Market.App</RootNamespace>
    <AssemblyName>Super_Market.App</AssemblyName>
    <TargetFrameworkVersion>v4.6.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <Deterministic>true</Deterministic>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Krypton.Navigator, Version=90.24.11.317, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Krypton.Navigator.90.24.11.317\lib\net462\Krypton.Navigator.dll</HintPath>
    </Reference>
    <Reference Include="Krypton.Toolkit, Version=90.24.11.317, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Krypton.Toolkit.90.24.11.317\lib\net462\Krypton.Toolkit.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.Design" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Form1.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form1.Designer.cs">
      <DependentUpon>Form1.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\FRM_CATEGORIES.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FRM_CATEGORIES.Designer.cs">
      <DependentUpon>FRM_CATEGORIES.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\FRM_CATEGORIES_LIST.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FRM_CATEGORIES_LIST.Designer.cs">
      <DependentUpon>FRM_CATEGORIES_LIST.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\FRM_CATEGORIE_EDIT.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FRM_CATEGORIE_EDIT.Designer.cs">
      <DependentUpon>FRM_CATEGORIE_EDIT.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\FRM_CLIENTS.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FRM_CLIENTS.Designer.cs">
      <DependentUpon>FRM_CLIENTS.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\FRM_PRODUITS_LIST.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FRM_PRODUITS_LIST.Designer.cs">
      <DependentUpon>FRM_PRODUITS_LIST.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\FRM_PRODUIT_EDIT.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FRM_PRODUIT_EDIT.Designer.cs">
      <DependentUpon>FRM_PRODUIT_EDIT.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\FRM_LOGIN.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FRM_LOGIN.Designer.cs">
      <DependentUpon>FRM_LOGIN.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\FRM_PRINCIPAL.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FRM_PRINCIPAL.Designer.cs">
      <DependentUpon>FRM_PRINCIPAL.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\FRM_SPLASH.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FRM_SPLASH.Designer.cs">
      <DependentUpon>FRM_SPLASH.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\FRM_UNITES.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FRM_UNITES.Designer.cs">
      <DependentUpon>FRM_UNITES.cs</DependentUpon>
    </Compile>
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <EmbeddedResource Include="Forms\FRM_CATEGORIES.resx">
      <DependentUpon>FRM_CATEGORIES.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\FRM_LOGIN.resx">
      <DependentUpon>FRM_LOGIN.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\FRM_PRINCIPAL.resx">
      <DependentUpon>FRM_PRINCIPAL.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\FRM_SPLASH.resx">
      <DependentUpon>FRM_SPLASH.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\FRM_UNITES.resx">
      <DependentUpon>FRM_UNITES.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <None Include="packages.config" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Super_Market.Business\Super_Market.Business.csproj">
      <Project>{e0efa91f-3a07-4cb0-893d-c07f57d0a95b}</Project>
      <Name>Super_Market.Business</Name>
    </ProjectReference>
    <ProjectReference Include="..\Super_Market.Core\Super_Market.Core.csproj">
      <Project>{ce1aee59-81e2-4971-a3f5-4e62246b8f29}</Project>
      <Name>Super_Market.Core</Name>
    </ProjectReference>
    <ProjectReference Include="..\Super_Market.Data\Super_Market.Data.csproj">
      <Project>{91e46799-c720-40a4-aaab-ac48a2d47f86}</Project>
      <Name>Super_Market.Data</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>
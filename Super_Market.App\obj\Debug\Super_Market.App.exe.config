﻿<?xml version="1.0" encoding="utf-8" ?>
<configuration>
    <startup>
        <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.6.2" />
    </startup>

    <connectionStrings>
        <add name="DefaultConnection"
             connectionString="Server=localhost;Database=zinstore_db;Uid=root;Pwd=;CharSet=utf8;"
             providerName="MySql.Data.MySqlClient" />
    </connectionStrings>

    <appSettings>
        <add key="CompanyName" value="ZinStore" />
        <add key="ApplicationTitle" value="Système de Gestion de Supermarché" />
        <add key="Version" value="1.0.0" />
        <add key="Culture" value="fr-DZ" />
        <add key="Currency" value="DZD" />
        <add key="TaxRate" value="19" />
        <add key="BackupPath" value="C:\ZinStore\Backups\" />
        <add key="ReportsPath" value="C:\ZinStore\Reports\" />
        <add key="LogsPath" value="C:\ZinStore\Logs\" />
    </appSettings>
</configuration>
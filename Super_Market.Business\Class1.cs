﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Super_Market.Core.Models;
using Super_Market.Data.Repositories;

namespace Super_Market.Business.Services
{
    public class UtilisateurService
    {
        private readonly UtilisateurRepository _utilisateurRepository;

        public UtilisateurService()
        {
            _utilisateurRepository = new UtilisateurRepository();
        }

        public async Task<IEnumerable<Utilisateur>> GetAllUtilisateursAsync()
        {
            return await _utilisateurRepository.GetAllAsync();
        }

        public async Task<Utilisateur> GetUtilisateurByIdAsync(int id)
        {
            return await _utilisateurRepository.GetByIdAsync(id);
        }

        public async Task<int> AjouterUtilisateurAsync(Utilisateur utilisateur)
        {
            // Validation
            if (string.IsNullOrWhiteSpace(utilisateur.NomUtilisateur))
                throw new ArgumentException("Le nom d'utilisateur est obligatoire");

            if (string.IsNullOrWhiteSpace(utilisateur.MotDePasse))
                throw new ArgumentException("Le mot de passe est obligatoire");

            if (string.IsNullOrWhiteSpace(utilisateur.Email))
                throw new ArgumentException("L'email est obligatoire");

            // Vérifier l'unicité du nom d'utilisateur
            var existingUser = await _utilisateurRepository.GetByNomUtilisateurAsync(utilisateur.NomUtilisateur);
            if (existingUser != null)
                throw new InvalidOperationException("Ce nom d'utilisateur existe déjà");

            // Hasher le mot de passe (simple pour l'exemple, utiliser BCrypt en production)
            utilisateur.MotDePasse = HashPassword(utilisateur.MotDePasse);

            return await _utilisateurRepository.AddAsync(utilisateur);
        }

        public async Task<bool> ModifierUtilisateurAsync(Utilisateur utilisateur)
        {
            // Validation
            if (utilisateur.Id <= 0)
                throw new ArgumentException("ID utilisateur invalide");

            var existingUser = await _utilisateurRepository.GetByIdAsync(utilisateur.Id);
            if (existingUser == null)
                throw new InvalidOperationException("Utilisateur introuvable");

            // Vérifier l'unicité du nom d'utilisateur
            var userWithSameName = await _utilisateurRepository.GetByNomUtilisateurAsync(utilisateur.NomUtilisateur);
            if (userWithSameName != null && userWithSameName.Id != utilisateur.Id)
                throw new InvalidOperationException("Ce nom d'utilisateur existe déjà");

            utilisateur.DateModification = DateTime.Now;
            return await _utilisateurRepository.UpdateAsync(utilisateur);
        }

        public async Task<bool> SupprimerUtilisateurAsync(int id)
        {
            var utilisateur = await _utilisateurRepository.GetByIdAsync(id);
            if (utilisateur == null)
                throw new InvalidOperationException("Utilisateur introuvable");

            return await _utilisateurRepository.DeleteAsync(id);
        }

        public async Task<Utilisateur> AuthentifierAsync(string nomUtilisateur, string motDePasse)
        {
            if (string.IsNullOrWhiteSpace(nomUtilisateur) || string.IsNullOrWhiteSpace(motDePasse))
                return null;

            var hashedPassword = HashPassword(motDePasse);
            var isValid = await _utilisateurRepository.ValidateLoginAsync(nomUtilisateur, hashedPassword);

            if (isValid)
            {
                var utilisateur = await _utilisateurRepository.GetByNomUtilisateurAsync(nomUtilisateur);
                await _utilisateurRepository.UpdateDerniereConnexionAsync(utilisateur.Id);
                return utilisateur;
            }

            return null;
        }

        public async Task<bool> DeconnecterAsync(int utilisateurId)
        {
            return await _utilisateurRepository.DeconnecterUtilisateurAsync(utilisateurId);
        }

        public async Task<IEnumerable<Utilisateur>> RechercherUtilisateursAsync(string terme)
        {
            if (string.IsNullOrWhiteSpace(terme))
                return await GetAllUtilisateursAsync();

            return await _utilisateurRepository.SearchAsync(terme);
        }

        private string HashPassword(string password)
        {
            // Simple hash pour l'exemple - utiliser BCrypt en production
            return Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes(password + "ZinStoreSalt"));
        }
    }
}

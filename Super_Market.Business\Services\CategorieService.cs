using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Super_Market.Core.Models;
using Super_Market.Data.Repositories;

namespace Super_Market.Business.Services
{
    public class CategorieService
    {
        private readonly CategorieRepository _categorieRepository;
        
        public CategorieService()
        {
            _categorieRepository = new CategorieRepository();
        }
        
        public async Task<IEnumerable<Categorie>> GetAllCategoriesAsync()
        {
            return await _categorieRepository.GetAllAsync();
        }
        
        public async Task<Categorie> GetCategorieByIdAsync(int id)
        {
            return await _categorieRepository.GetByIdAsync(id);
        }
        
        public async Task<IEnumerable<Categorie>> GetCategoriesPrincipalesAsync()
        {
            return await _categorieRepository.GetCategoriesPrincipalesAsync();
        }
        
        public async Task<IEnumerable<Categorie>> GetSousCategoriesAsync(int categorieParentId)
        {
            return await _categorieRepository.GetSousCategoriesAsync(categorieParentId);
        }
        
        public async Task<int> AjouterCategorieAsync(Categorie categorie)
        {
            // Validation
            if (string.IsNullOrWhiteSpace(categorie.Nom))
                throw new ArgumentException("Le nom de la catégorie est obligatoire");
            
            // Vérifier l'unicité du code si fourni
            if (!string.IsNullOrWhiteSpace(categorie.Code))
            {
                var existingCategory = await _categorieRepository.ExistsByCodeAsync(categorie.Code);
                if (existingCategory)
                    throw new InvalidOperationException("Ce code de catégorie existe déjà");
            }
            
            // Vérifier que la catégorie parent existe si spécifiée
            if (categorie.CategorieParentId.HasValue)
            {
                var categorieParent = await _categorieRepository.GetByIdAsync(categorie.CategorieParentId.Value);
                if (categorieParent == null)
                    throw new InvalidOperationException("La catégorie parent spécifiée n'existe pas");
            }
            
            return await _categorieRepository.AddAsync(categorie);
        }
        
        public async Task<bool> ModifierCategorieAsync(Categorie categorie)
        {
            // Validation
            if (categorie.Id <= 0)
                throw new ArgumentException("ID catégorie invalide");
                
            var existingCategory = await _categorieRepository.GetByIdAsync(categorie.Id);
            if (existingCategory == null)
                throw new InvalidOperationException("Catégorie introuvable");
            
            // Vérifier l'unicité du code
            if (!string.IsNullOrWhiteSpace(categorie.Code))
            {
                var categoryWithSameCode = await _categorieRepository.ExistsByCodeAsync(categorie.Code, categorie.Id);
                if (categoryWithSameCode)
                    throw new InvalidOperationException("Ce code de catégorie existe déjà");
            }
            
            // Vérifier que la catégorie parent existe si spécifiée
            if (categorie.CategorieParentId.HasValue)
            {
                var categorieParent = await _categorieRepository.GetByIdAsync(categorie.CategorieParentId.Value);
                if (categorieParent == null)
                    throw new InvalidOperationException("La catégorie parent spécifiée n'existe pas");
                    
                // Vérifier qu'on ne crée pas une référence circulaire
                if (await EstReferenceCirculaireAsync(categorie.Id, categorie.CategorieParentId.Value))
                    throw new InvalidOperationException("Cette modification créerait une référence circulaire");
            }
            
            categorie.DateModification = DateTime.Now;
            return await _categorieRepository.UpdateAsync(categorie);
        }
        
        public async Task<bool> SupprimerCategorieAsync(int id)
        {
            var categorie = await _categorieRepository.GetByIdAsync(id);
            if (categorie == null)
                throw new InvalidOperationException("Catégorie introuvable");
            
            // Vérifier qu'il n'y a pas de sous-catégories
            var hasSousCategories = await _categorieRepository.HasSousCategoriesAsync(id);
            if (hasSousCategories)
                throw new InvalidOperationException("Impossible de supprimer une catégorie qui contient des sous-catégories");
            
            // Vérifier qu'il n'y a pas de produits
            var hasProduits = await _categorieRepository.HasProduitsAsync(id);
            if (hasProduits)
                throw new InvalidOperationException("Impossible de supprimer une catégorie qui contient des produits");
                
            return await _categorieRepository.DeleteAsync(id);
        }
        
        public async Task<IEnumerable<Categorie>> RechercherCategoriesAsync(string terme)
        {
            if (string.IsNullOrWhiteSpace(terme))
                return await GetAllCategoriesAsync();
                
            return await _categorieRepository.SearchAsync(terme);
        }
        
        public async Task<IEnumerable<Categorie>> GetHierarchieCompleteAsync()
        {
            return await _categorieRepository.GetHierarchieCompleteAsync();
        }
        
        public async Task<bool> PeutEtreSupprimeeAsync(int categorieId)
        {
            var hasSousCategories = await _categorieRepository.HasSousCategoriesAsync(categorieId);
            var hasProduits = await _categorieRepository.HasProduitsAsync(categorieId);
            
            return !hasSousCategories && !hasProduits;
        }
        
        private async Task<bool> EstReferenceCirculaireAsync(int categorieId, int categorieParentId)
        {
            // Vérifier si la catégorie parent est en fait un descendant de la catégorie actuelle
            var currentParentId = categorieParentId;
            
            while (currentParentId.HasValue)
            {
                if (currentParentId.Value == categorieId)
                    return true;
                    
                var parent = await _categorieRepository.GetByIdAsync(currentParentId.Value);
                if (parent == null)
                    break;
                    
                currentParentId = parent.CategorieParentId;
            }
            
            return false;
        }
        
        public async Task<string> GenererCodeAutomatiqueAsync()
        {
            var categories = await _categorieRepository.GetAllAsync();
            var maxCode = 0;
            
            foreach (var cat in categories)
            {
                if (!string.IsNullOrWhiteSpace(cat.Code) && cat.Code.StartsWith("CAT"))
                {
                    var codeNumber = cat.Code.Substring(3);
                    if (int.TryParse(codeNumber, out int number) && number > maxCode)
                    {
                        maxCode = number;
                    }
                }
            }
            
            return $"CAT{(maxCode + 1):D3}";
        }
        
        public async Task<int> GetNombreProduitsAsync(int categorieId)
        {
            return await _categorieRepository.HasProduitsAsync(categorieId) ? 1 : 0; // Simplification
        }
        
        public async Task<int> GetNombreSousCategoriesAsync(int categorieId)
        {
            var sousCategories = await _categorieRepository.GetSousCategoriesAsync(categorieId);
            return sousCategories?.Count() ?? 0;
        }
    }
}

using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Super_Market.Core.Models;
using Super_Market.Data.Repositories;

namespace Super_Market.Business.Services
{
    public class ClientService
    {
        private readonly ClientRepository _clientRepository;
        
        public ClientService()
        {
            _clientRepository = new ClientRepository();
        }
        
        public async Task<IEnumerable<Client>> GetAllClientsAsync()
        {
            return await _clientRepository.GetAllAsync();
        }
        
        public async Task<Client> GetClientByIdAsync(int id)
        {
            return await _clientRepository.GetByIdAsync(id);
        }
        
        public async Task<int> AjouterClientAsync(Client client)
        {
            // Validation
            if (string.IsNullOrWhiteSpace(client.Nom) && string.IsNullOrWhiteSpace(client.RaisonSociale))
                throw new ArgumentException("Le nom ou la raison sociale est obligatoire");
                
            if (string.IsNullOrWhiteSpace(client.Email))
                throw new ArgumentException("L'email est obligatoire");
            
            // Vérifier l'unicité de l'email
            var existingClient = await _clientRepository.GetByEmailAsync(client.Email);
            if (existingClient != null)
                throw new InvalidOperationException("Un client avec cet email existe déjà");
            
            // Validation spécifique pour les entreprises
            if (client.EstEntreprise)
            {
                if (string.IsNullOrWhiteSpace(client.RaisonSociale))
                    throw new ArgumentException("La raison sociale est obligatoire pour une entreprise");
                    
                if (string.IsNullOrWhiteSpace(client.NumeroRegistreCommerce))
                    throw new ArgumentException("Le numéro de registre de commerce est obligatoire pour une entreprise");
            }
            else
            {
                if (string.IsNullOrWhiteSpace(client.Nom))
                    throw new ArgumentException("Le nom est obligatoire pour un particulier");
                    
                if (string.IsNullOrWhiteSpace(client.Prenom))
                    throw new ArgumentException("Le prénom est obligatoire pour un particulier");
            }
            
            return await _clientRepository.AddAsync(client);
        }
        
        public async Task<bool> ModifierClientAsync(Client client)
        {
            // Validation
            if (client.Id <= 0)
                throw new ArgumentException("ID client invalide");
                
            var existingClient = await _clientRepository.GetByIdAsync(client.Id);
            if (existingClient == null)
                throw new InvalidOperationException("Client introuvable");
            
            // Vérifier l'unicité de l'email
            var clientWithSameEmail = await _clientRepository.GetByEmailAsync(client.Email);
            if (clientWithSameEmail != null && clientWithSameEmail.Id != client.Id)
                throw new InvalidOperationException("Un client avec cet email existe déjà");
            
            client.DateModification = DateTime.Now;
            return await _clientRepository.UpdateAsync(client);
        }
        
        public async Task<bool> SupprimerClientAsync(int id)
        {
            var client = await _clientRepository.GetByIdAsync(id);
            if (client == null)
                throw new InvalidOperationException("Client introuvable");
                
            return await _clientRepository.DeleteAsync(id);
        }
        
        public async Task<IEnumerable<Client>> RechercherClientsAsync(string terme)
        {
            if (string.IsNullOrWhiteSpace(terme))
                return await GetAllClientsAsync();
                
            return await _clientRepository.SearchAsync(terme);
        }
        
        public async Task<IEnumerable<Client>> GetClientsEntrepriseAsync()
        {
            return await _clientRepository.GetClientsEntrepriseAsync();
        }
        
        public async Task<IEnumerable<Client>> GetClientsParticuliersAsync()
        {
            return await _clientRepository.GetClientsParticuliersAsync();
        }
        
        public async Task<bool> CrediterCompteAsync(int clientId, decimal montant, string motif)
        {
            if (montant <= 0)
                throw new ArgumentException("Le montant doit être positif");
                
            var client = await _clientRepository.GetByIdAsync(clientId);
            if (client == null)
                throw new InvalidOperationException("Client introuvable");
            
            var nouveauSolde = client.SoldeCompte + montant;
            return await _clientRepository.UpdateSoldeAsync(clientId, nouveauSolde);
        }
        
        public async Task<bool> DebiterCompteAsync(int clientId, decimal montant, string motif)
        {
            if (montant <= 0)
                throw new ArgumentException("Le montant doit être positif");
                
            var client = await _clientRepository.GetByIdAsync(clientId);
            if (client == null)
                throw new InvalidOperationException("Client introuvable");
            
            var nouveauSolde = client.SoldeCompte - montant;
            
            // Vérifier la limite de crédit
            if (nouveauSolde < -client.LimiteCredit)
                throw new InvalidOperationException("Limite de crédit dépassée");
            
            return await _clientRepository.UpdateSoldeAsync(clientId, nouveauSolde);
        }
        
        public async Task<bool> AjouterPointsFideliteAsync(int clientId, decimal montantAchat)
        {
            var client = await _clientRepository.GetByIdAsync(clientId);
            if (client == null)
                throw new InvalidOperationException("Client introuvable");
            
            // 1 point par 100 DZD d'achat
            var pointsGagnes = (int)(montantAchat / 100);
            var nouveauxPoints = client.PointsFidelite + pointsGagnes;
            
            return await _clientRepository.UpdatePointsFideliteAsync(clientId, nouveauxPoints);
        }
        
        public async Task<bool> UtiliserPointsFideliteAsync(int clientId, int pointsUtilises)
        {
            if (pointsUtilises <= 0)
                throw new ArgumentException("Le nombre de points doit être positif");
                
            var client = await _clientRepository.GetByIdAsync(clientId);
            if (client == null)
                throw new InvalidOperationException("Client introuvable");
            
            if (client.PointsFidelite < pointsUtilises)
                throw new InvalidOperationException("Points de fidélité insuffisants");
            
            var nouveauxPoints = client.PointsFidelite - pointsUtilises;
            return await _clientRepository.UpdatePointsFideliteAsync(clientId, nouveauxPoints);
        }
        
        public async Task<IEnumerable<Client>> GetClientsFidelesAsync(int pointsMinimum = 100)
        {
            return await _clientRepository.GetClientsFidelesAsync(pointsMinimum);
        }
        
        public async Task<decimal> GetChiffreAffairesClientAsync(int clientId, DateTime? dateDebut = null, DateTime? dateFin = null)
        {
            return await _clientRepository.GetChiffreAffairesClientAsync(clientId, dateDebut, dateFin);
        }
        
        public decimal CalculerRemisePointsFidelite(int pointsFidelite)
        {
            // 100 points = 10 DZD de remise
            return (pointsFidelite / 100) * 10;
        }
        
        public bool PeutBeneficierCredit(Client client, decimal montant)
        {
            return (client.SoldeCompte - montant) >= -client.LimiteCredit;
        }
        
        public string GenererCodeClient(Client client)
        {
            if (client.EstEntreprise)
            {
                return $"ENT{client.Id:D6}";
            }
            else
            {
                var initiales = $"{client.Nom?.Substring(0, 1)}{client.Prenom?.Substring(0, 1)}".ToUpper();
                return $"CLI{initiales}{client.Id:D4}";
            }
        }
    }
}

using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Super_Market.Core.Models;
using Super_Market.Data.Repositories;

namespace Super_Market.Business.Services
{
    public class ProduitService
    {
        private readonly ProduitRepository _produitRepository;
        private readonly CategorieRepository _categorieRepository;
        
        public ProduitService()
        {
            _produitRepository = new ProduitRepository();
            _categorieRepository = new CategorieRepository();
        }
        
        public async Task<IEnumerable<Produit>> GetAllProduitsAsync()
        {
            return await _produitRepository.GetAllAsync();
        }
        
        public async Task<Produit> GetProduitByIdAsync(int id)
        {
            return await _produitRepository.GetByIdAsync(id);
        }
        
        public async Task<Produit> GetProduitByCodeBarreAsync(string codeBarre)
        {
            if (string.IsNullOrWhiteSpace(codeBarre))
                return null;
                
            return await _produitRepository.GetByCodeBarreAsync(codeBarre);
        }
        
        public async Task<int> AjouterProduitAsync(Produit produit)
        {
            // Validation
            if (string.IsNullOrWhiteSpace(produit.Nom))
                throw new ArgumentException("Le nom du produit est obligatoire");
                
            if (string.IsNullOrWhiteSpace(produit.CodeBarre))
                throw new ArgumentException("Le code-barres est obligatoire");
                
            if (produit.CategorieId <= 0)
                throw new ArgumentException("La catégorie est obligatoire");
                
            if (produit.PrixAchat < 0)
                throw new ArgumentException("Le prix d'achat ne peut pas être négatif");
                
            if (produit.PrixVente < 0)
                throw new ArgumentException("Le prix de vente ne peut pas être négatif");
            
            // Vérifier que la catégorie existe
            var categorie = await _categorieRepository.GetByIdAsync(produit.CategorieId);
            if (categorie == null)
                throw new InvalidOperationException("La catégorie spécifiée n'existe pas");
            
            // Vérifier l'unicité du code-barres
            var existingProduct = await _produitRepository.ExistsByCodeBarreAsync(produit.CodeBarre);
            if (existingProduct)
                throw new InvalidOperationException("Ce code-barres existe déjà");
            
            // Validation des prix
            if (produit.PrixVente <= produit.PrixAchat)
                throw new ArgumentException("Le prix de vente doit être supérieur au prix d'achat");
            
            return await _produitRepository.AddAsync(produit);
        }
        
        public async Task<bool> ModifierProduitAsync(Produit produit)
        {
            // Validation
            if (produit.Id <= 0)
                throw new ArgumentException("ID produit invalide");
                
            var existingProduct = await _produitRepository.GetByIdAsync(produit.Id);
            if (existingProduct == null)
                throw new InvalidOperationException("Produit introuvable");
            
            // Vérifier l'unicité du code-barres
            var productWithSameBarcode = await _produitRepository.ExistsByCodeBarreAsync(produit.CodeBarre, produit.Id);
            if (productWithSameBarcode)
                throw new InvalidOperationException("Ce code-barres existe déjà");
            
            // Vérifier que la catégorie existe
            var categorie = await _categorieRepository.GetByIdAsync(produit.CategorieId);
            if (categorie == null)
                throw new InvalidOperationException("La catégorie spécifiée n'existe pas");
            
            produit.DateModification = DateTime.Now;
            return await _produitRepository.UpdateAsync(produit);
        }
        
        public async Task<bool> SupprimerProduitAsync(int id)
        {
            var produit = await _produitRepository.GetByIdAsync(id);
            if (produit == null)
                throw new InvalidOperationException("Produit introuvable");
                
            return await _produitRepository.DeleteAsync(id);
        }
        
        public async Task<IEnumerable<Produit>> RechercherProduitsAsync(string terme)
        {
            if (string.IsNullOrWhiteSpace(terme))
                return await GetAllProduitsAsync();
                
            return await _produitRepository.SearchAsync(terme);
        }
        
        public async Task<IEnumerable<Produit>> GetProduitsByCategorieAsync(int categorieId)
        {
            return await _produitRepository.GetByCategorieAsync(categorieId);
        }
        
        public async Task<IEnumerable<Produit>> GetProduitsEnPromotionAsync()
        {
            return await _produitRepository.GetProduitsEnPromotionAsync();
        }
        
        public async Task<IEnumerable<Produit>> GetProduitsStockFaibleAsync()
        {
            return await _produitRepository.GetProduitsStockFaibleAsync();
        }
        
        public async Task<bool> AppliquerPromotionAsync(int produitId, decimal prixPromotion, DateTime dateDebut, DateTime dateFin)
        {
            var produit = await _produitRepository.GetByIdAsync(produitId);
            if (produit == null)
                throw new InvalidOperationException("Produit introuvable");
            
            if (prixPromotion <= 0)
                throw new ArgumentException("Le prix de promotion doit être positif");
                
            if (prixPromotion >= produit.PrixVente)
                throw new ArgumentException("Le prix de promotion doit être inférieur au prix normal");
                
            if (dateDebut >= dateFin)
                throw new ArgumentException("La date de début doit être antérieure à la date de fin");
            
            produit.PrixPromotion = prixPromotion;
            produit.DateDebutPromotion = dateDebut;
            produit.DateFinPromotion = dateFin;
            produit.DateModification = DateTime.Now;
            
            return await _produitRepository.UpdateAsync(produit);
        }
        
        public async Task<bool> AnnulerPromotionAsync(int produitId)
        {
            var produit = await _produitRepository.GetByIdAsync(produitId);
            if (produit == null)
                throw new InvalidOperationException("Produit introuvable");
            
            produit.PrixPromotion = null;
            produit.DateDebutPromotion = null;
            produit.DateFinPromotion = null;
            produit.DateModification = DateTime.Now;
            
            return await _produitRepository.UpdateAsync(produit);
        }
        
        public decimal CalculerPrixVenteActuel(Produit produit)
        {
            if (produit.PrixPromotion.HasValue && 
                produit.DateDebutPromotion.HasValue && 
                produit.DateFinPromotion.HasValue &&
                DateTime.Now >= produit.DateDebutPromotion.Value && 
                DateTime.Now <= produit.DateFinPromotion.Value)
            {
                return produit.PrixPromotion.Value;
            }
            
            return produit.PrixVente;
        }
        
        public bool EstEnPromotion(Produit produit)
        {
            return produit.PrixPromotion.HasValue && 
                   produit.DateDebutPromotion.HasValue && 
                   produit.DateFinPromotion.HasValue &&
                   DateTime.Now >= produit.DateDebutPromotion.Value && 
                   DateTime.Now <= produit.DateFinPromotion.Value;
        }
    }
}

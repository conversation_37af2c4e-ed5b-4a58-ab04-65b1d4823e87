using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Super_Market.Core.Models;
using Super_Market.Data.Repositories;

namespace Super_Market.Business.Services
{
    public class StockService
    {
        private readonly StockRepository _stockRepository;
        private readonly ProduitRepository _produitRepository;
        
        public StockService()
        {
            _stockRepository = new StockRepository();
            _produitRepository = new ProduitRepository();
        }
        
        public async Task<IEnumerable<Stock>> GetAllStocksAsync()
        {
            return await _stockRepository.GetAllAsync();
        }
        
        public async Task<Stock> GetStockByIdAsync(int id)
        {
            return await _stockRepository.GetByIdAsync(id);
        }
        
        public async Task<Stock> GetStockByProduitAsync(int produitId)
        {
            return await _stockRepository.GetByProduitIdAsync(produitId);
        }
        
        public async Task<int> CreerStockAsync(Stock stock)
        {
            // Validation
            if (stock.ProduitId <= 0)
                throw new ArgumentException("ID produit invalide");
                
            if (stock.QuantiteDisponible < 0)
                throw new ArgumentException("La quantité disponible ne peut pas être négative");
            
            // Vérifier que le produit existe
            var produit = await _produitRepository.GetByIdAsync(stock.ProduitId);
            if (produit == null)
                throw new InvalidOperationException("Le produit spécifié n'existe pas");
            
            // Vérifier qu'il n'y a pas déjà un stock pour ce produit
            var stockExistant = await _stockRepository.GetByProduitIdAsync(stock.ProduitId);
            if (stockExistant != null)
                throw new InvalidOperationException("Un stock existe déjà pour ce produit");
            
            // Calculer la valeur du stock
            stock.ValeurStock = stock.QuantiteDisponible * produit.PrixAchat;
            stock.CoutMoyenPondere = produit.PrixAchat;
            
            return await _stockRepository.AddAsync(stock);
        }
        
        public async Task<bool> ModifierStockAsync(Stock stock)
        {
            // Validation
            if (stock.Id <= 0)
                throw new ArgumentException("ID stock invalide");
                
            var stockExistant = await _stockRepository.GetByIdAsync(stock.Id);
            if (stockExistant == null)
                throw new InvalidOperationException("Stock introuvable");
            
            // Recalculer la valeur du stock
            var produit = await _produitRepository.GetByIdAsync(stock.ProduitId);
            if (produit != null)
            {
                stock.ValeurStock = stock.QuantiteDisponible * (stock.CoutMoyenPondere ?? produit.PrixAchat);
            }
            
            stock.DateModification = DateTime.Now;
            return await _stockRepository.UpdateAsync(stock);
        }
        
        public async Task<bool> AjusterStockAsync(int produitId, decimal quantiteAjustement, string motif, int utilisateurId)
        {
            if (quantiteAjustement == 0)
                throw new ArgumentException("La quantité d'ajustement ne peut pas être zéro");
                
            var stock = await _stockRepository.GetByProduitIdAsync(produitId);
            if (stock == null)
                throw new InvalidOperationException("Aucun stock trouvé pour ce produit");
            
            // Vérifier que l'ajustement ne rend pas le stock négatif
            if (stock.QuantiteDisponible + quantiteAjustement < 0)
                throw new InvalidOperationException("L'ajustement rendrait le stock négatif");
            
            return await _stockRepository.AjusterStockAsync(produitId, quantiteAjustement, motif);
        }
        
        public async Task<bool> EntrerStockAsync(int produitId, decimal quantite, decimal coutUnitaire, string reference, int utilisateurId)
        {
            if (quantite <= 0)
                throw new ArgumentException("La quantité doit être positive");
                
            if (coutUnitaire < 0)
                throw new ArgumentException("Le coût unitaire ne peut pas être négatif");
            
            var stock = await _stockRepository.GetByProduitIdAsync(produitId);
            if (stock == null)
            {
                // Créer un nouveau stock
                var produit = await _produitRepository.GetByIdAsync(produitId);
                if (produit == null)
                    throw new InvalidOperationException("Produit introuvable");
                
                stock = new Stock
                {
                    ProduitId = produitId,
                    QuantiteDisponible = quantite,
                    CoutMoyenPondere = coutUnitaire,
                    ValeurStock = quantite * coutUnitaire,
                    DateCreation = DateTime.Now,
                    UtilisateurCreation = utilisateurId.ToString(),
                    EstActif = true
                };
                
                await _stockRepository.AddAsync(stock);
            }
            else
            {
                // Mettre à jour le stock existant avec coût moyen pondéré
                var ancienneValeur = stock.QuantiteDisponible * (stock.CoutMoyenPondere ?? 0);
                var nouvelleValeur = quantite * coutUnitaire;
                var nouvelleQuantite = stock.QuantiteDisponible + quantite;
                
                stock.CoutMoyenPondere = nouvelleQuantite > 0 ? (ancienneValeur + nouvelleValeur) / nouvelleQuantite : coutUnitaire;
                stock.QuantiteDisponible = nouvelleQuantite;
                stock.ValeurStock = nouvelleQuantite * stock.CoutMoyenPondere.Value;
                
                await _stockRepository.UpdateAsync(stock);
            }
            
            return true;
        }
        
        public async Task<bool> SortirStockAsync(int produitId, decimal quantite, string motif, int utilisateurId)
        {
            if (quantite <= 0)
                throw new ArgumentException("La quantité doit être positive");
            
            var stock = await _stockRepository.GetByProduitIdAsync(produitId);
            if (stock == null)
                throw new InvalidOperationException("Aucun stock trouvé pour ce produit");
            
            if (stock.QuantiteDisponibleVente < quantite)
                throw new InvalidOperationException("Stock insuffisant");
            
            return await _stockRepository.AjusterStockAsync(produitId, -quantite, motif);
        }
        
        public async Task<bool> ReserverStockAsync(int produitId, decimal quantite)
        {
            if (quantite <= 0)
                throw new ArgumentException("La quantité doit être positive");
            
            var stock = await _stockRepository.GetByProduitIdAsync(produitId);
            if (stock == null)
                throw new InvalidOperationException("Aucun stock trouvé pour ce produit");
            
            if (stock.QuantiteDisponibleVente < quantite)
                throw new InvalidOperationException("Stock insuffisant pour la réservation");
            
            return await _stockRepository.ReserverStockAsync(produitId, quantite);
        }
        
        public async Task<bool> LibererStockAsync(int produitId, decimal quantite)
        {
            if (quantite <= 0)
                throw new ArgumentException("La quantité doit être positive");
            
            return await _stockRepository.LibererStockAsync(produitId, quantite);
        }
        
        public async Task<IEnumerable<Stock>> GetStocksFaiblesAsync()
        {
            return await _stockRepository.GetStocksFaiblesAsync();
        }
        
        public async Task<IEnumerable<Stock>> GetSurstocksAsync()
        {
            return await _stockRepository.GetSurstocksAsync();
        }
        
        public async Task<decimal> GetValeurTotaleStockAsync()
        {
            return await _stockRepository.GetValeurTotaleStockAsync();
        }
        
        public async Task<int> GetNombreProduitsEnStockAsync()
        {
            return await _stockRepository.GetNombreProduitsEnStockAsync();
        }
        
        public async Task<int> GetNombreProduitsRuptureAsync()
        {
            return await _stockRepository.GetNombreProduitsRuptureAsync();
        }
        
        public async Task<IEnumerable<dynamic>> GetStatistiquesStockAsync()
        {
            return await _stockRepository.GetStatistiquesStockAsync();
        }
        
        public async Task<bool> EffectuerInventaireAsync(int produitId, decimal quantiteComptee, string notes, int utilisateurId)
        {
            var stock = await _stockRepository.GetByProduitIdAsync(produitId);
            if (stock == null)
                throw new InvalidOperationException("Aucun stock trouvé pour ce produit");
            
            var ecart = quantiteComptee - stock.QuantiteDisponible;
            
            if (ecart != 0)
            {
                var motif = $"Inventaire - Écart: {ecart:F2}. {notes}";
                await _stockRepository.AjusterStockAsync(produitId, ecart, motif);
            }
            
            // Mettre à jour la date du dernier inventaire
            stock.DateDernierInventaire = DateTime.Now;
            await _stockRepository.UpdateAsync(stock);
            
            return true;
        }
        
        public bool EstEnRupture(Stock stock)
        {
            return stock.EstEnRupture;
        }
        
        public bool EstSurstock(Stock stock)
        {
            return stock.EstSurstock;
        }
        
        public decimal CalculerRotationStock(decimal quantiteVendue, decimal stockMoyen, int nombreJours)
        {
            if (stockMoyen == 0) return 0;
            return (quantiteVendue / stockMoyen) * (365m / nombreJours);
        }
        
        public int CalculerJoursCouverture(decimal stockActuel, decimal consommationMoyenneJour)
        {
            if (consommationMoyenneJour == 0) return int.MaxValue;
            return (int)(stockActuel / consommationMoyenneJour);
        }
    }
}

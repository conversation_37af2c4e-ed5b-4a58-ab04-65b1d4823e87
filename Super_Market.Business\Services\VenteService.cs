using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Super_Market.Core.Models;
using Super_Market.Core.Enums;
using Super_Market.Data.Repositories;

namespace Super_Market.Business.Services
{
    public class VenteService
    {
        private readonly VenteRepository _venteRepository;
        private readonly StockRepository _stockRepository;
        private readonly ClientRepository _clientRepository;
        
        public VenteService()
        {
            _venteRepository = new VenteRepository();
            _stockRepository = new StockRepository();
            _clientRepository = new ClientRepository();
        }
        
        public async Task<IEnumerable<Vente>> GetAllVentesAsync()
        {
            return await _venteRepository.GetAllAsync();
        }
        
        public async Task<Vente> GetVenteByIdAsync(int id)
        {
            return await _venteRepository.GetByIdAsync(id);
        }
        
        public async Task<int> CreerVenteAsync(Vente vente, List<VenteDetail> details)
        {
            // Validation
            if (vente == null)
                throw new ArgumentException("La vente ne peut pas être nulle");
                
            if (details == null || details.Count == 0)
                throw new ArgumentException("Une vente doit contenir au moins un article");
            
            // Générer le numéro de facture
            vente.NumeroFacture = await _venteRepository.GenererNumeroFactureAsync();
            
            // Calculer les totaux
            CalculerTotauxVente(vente, details);
            
            // Vérifier la disponibilité du stock
            foreach (var detail in details)
            {
                var stock = await _stockRepository.GetByProduitIdAsync(detail.ProduitId);
                if (stock == null || stock.QuantiteDisponibleVente < detail.Quantite)
                    throw new InvalidOperationException($"Stock insuffisant pour le produit ID {detail.ProduitId}");
            }
            
            // Vérifier la limite de crédit si c'est un client
            if (vente.ClientId.HasValue)
            {
                var client = await _clientRepository.GetByIdAsync(vente.ClientId.Value);
                if (client != null && vente.ModePaiement == ModePaiement.Credit)
                {
                    var nouveauSolde = client.SoldeCompte - vente.Total;
                    if (nouveauSolde < -client.LimiteCredit)
                        throw new InvalidOperationException("Limite de crédit du client dépassée");
                }
            }
            
            // Créer la vente
            var venteId = await _venteRepository.AddAsync(vente);
            
            // Ajouter les détails et mettre à jour le stock
            foreach (var detail in details)
            {
                detail.VenteId = venteId;
                // TODO: Ajouter les détails de vente
                
                // Mettre à jour le stock
                var stock = await _stockRepository.GetByProduitIdAsync(detail.ProduitId);
                var nouvelleQuantite = stock.QuantiteDisponible - detail.Quantite;
                await _stockRepository.UpdateQuantiteAsync(detail.ProduitId, nouvelleQuantite);
            }
            
            return venteId;
        }
        
        public async Task<bool> ValiderVenteAsync(int venteId)
        {
            var vente = await _venteRepository.GetByIdAsync(venteId);
            if (vente == null)
                throw new InvalidOperationException("Vente introuvable");
            
            if (vente.Statut != StatutVente.EnCours)
                throw new InvalidOperationException("Seules les ventes en cours peuvent être validées");
            
            vente.Statut = StatutVente.Validee;
            vente.DateModification = DateTime.Now;
            
            var result = await _venteRepository.UpdateAsync(vente);
            
            // Mettre à jour le compte client si applicable
            if (result && vente.ClientId.HasValue)
            {
                var client = await _clientRepository.GetByIdAsync(vente.ClientId.Value);
                if (client != null)
                {
                    // Débiter le compte si c'est un crédit
                    if (vente.ModePaiement == ModePaiement.Credit)
                    {
                        var nouveauSolde = client.SoldeCompte - vente.Total;
                        await _clientRepository.UpdateSoldeAsync(client.Id, nouveauSolde);
                    }
                    
                    // Ajouter des points de fidélité
                    var pointsGagnes = (int)(vente.Total / 100);
                    var nouveauxPoints = client.PointsFidelite + pointsGagnes;
                    await _clientRepository.UpdatePointsFideliteAsync(client.Id, nouveauxPoints);
                }
            }
            
            return result;
        }
        
        public async Task<bool> AnnulerVenteAsync(int venteId, string motif)
        {
            var vente = await _venteRepository.GetByIdAsync(venteId);
            if (vente == null)
                throw new InvalidOperationException("Vente introuvable");
            
            if (vente.Statut == StatutVente.Annulee)
                throw new InvalidOperationException("Cette vente est déjà annulée");
            
            // Remettre le stock
            // TODO: Récupérer les détails de la vente et remettre le stock
            
            return await _venteRepository.AnnulerVenteAsync(venteId, motif);
        }
        
        public async Task<IEnumerable<Vente>> GetVentesByDateAsync(DateTime date)
        {
            return await _venteRepository.GetVentesByDateAsync(date);
        }
        
        public async Task<IEnumerable<Vente>> GetVentesByPeriodeAsync(DateTime dateDebut, DateTime dateFin)
        {
            return await _venteRepository.GetVentesByPeriodeAsync(dateDebut, dateFin);
        }
        
        public async Task<IEnumerable<Vente>> GetVentesByClientAsync(int clientId)
        {
            return await _venteRepository.GetVentesByClientAsync(clientId);
        }
        
        public async Task<decimal> GetChiffreAffairesAsync(DateTime? dateDebut = null, DateTime? dateFin = null)
        {
            return await _venteRepository.GetChiffreAffairesAsync(dateDebut, dateFin);
        }
        
        public async Task<int> GetNombreVentesAsync(DateTime? dateDebut = null, DateTime? dateFin = null)
        {
            return await _venteRepository.GetNombreVentesAsync(dateDebut, dateFin);
        }
        
        public async Task<decimal> GetPanierMoyenAsync(DateTime? dateDebut = null, DateTime? dateFin = null)
        {
            var chiffreAffaires = await GetChiffreAffairesAsync(dateDebut, dateFin);
            var nombreVentes = await GetNombreVentesAsync(dateDebut, dateFin);
            
            return nombreVentes > 0 ? chiffreAffaires / nombreVentes : 0;
        }
        
        public async Task<IEnumerable<dynamic>> GetStatistiquesVentesAsync(DateTime dateDebut, DateTime dateFin)
        {
            return await _venteRepository.GetStatistiquesVentesAsync(dateDebut, dateFin);
        }
        
        private void CalculerTotauxVente(Vente vente, List<VenteDetail> details)
        {
            decimal sousTotal = 0;
            decimal montantTVA = 0;
            
            foreach (var detail in details)
            {
                // Calculer le sous-total de la ligne
                detail.SousTotal = detail.Quantite * detail.PrixUnitaire - detail.Remise;
                
                // Calculer la TVA de la ligne
                detail.MontantTVA = detail.SousTotal * (detail.TauxTVA / 100);
                
                sousTotal += detail.SousTotal;
                montantTVA += detail.MontantTVA;
            }
            
            vente.SousTotal = sousTotal;
            vente.MontantTVA = montantTVA;
            vente.Total = sousTotal + montantTVA - vente.Remise;
            
            // Calculer le montant rendu
            if (vente.MontantPaye > vente.Total)
            {
                vente.MontantRendu = vente.MontantPaye - vente.Total;
            }
        }
        
        public decimal CalculerRemisePointsFidelite(int pointsFidelite)
        {
            // 100 points = 10 DZD de remise
            return (pointsFidelite / 100) * 10;
        }
        
        public bool PeutAppliquerRemise(decimal montantVente, decimal remise)
        {
            // Maximum 50% de remise
            return remise <= (montantVente * 0.5m);
        }
        
        public async Task<string> GenererNumeroFactureAsync()
        {
            return await _venteRepository.GenererNumeroFactureAsync();
        }
    }
}

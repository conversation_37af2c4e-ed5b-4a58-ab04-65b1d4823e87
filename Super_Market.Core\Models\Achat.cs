﻿using System;
using System.ComponentModel.DataAnnotations;
using System.Collections.Generic;
using Super_Market.Core.Enums;

namespace Super_Market.Core.Models
{
    public class Achat : BaseEntity
    {
        [Required]
        [StringLength(20)]
        public string NumeroCommande { get; set; }

        [StringLength(20)]
        public string NumeroFactureFournisseur { get; set; }

        [Required]
        public DateTime DateCommande { get; set; } = DateTime.Now;

        public DateTime? DateLivraison { get; set; }

        public DateTime? DateFacture { get; set; }

        [Required]
        public int FournisseurId { get; set; }

        public virtual Fournisseur Fournisseur { get; set; }

        [Required]
        public int UtilisateurId { get; set; }

        public virtual Utilisateur Utilisateur { get; set; }

        [Required]
        public decimal SousTotal { get; set; }

        public decimal Remise { get; set; } = 0;

        public decimal MontantTVA { get; set; } = 0;

        [Required]
        public decimal Total { get; set; }

        public decimal MontantPaye { get; set; } = 0;

        [Required]
        public StatutAchat Statut { get; set; } = StatutAchat.Commande;

        [StringLength(500)]
        public string Notes { get; set; }

        public virtual ICollection<AchatDetail> AchatDetails { get; set; }

        public Achat()
        {
            AchatDetails = new HashSet<AchatDetail>();
        }
    }
}

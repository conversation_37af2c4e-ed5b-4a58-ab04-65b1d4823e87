﻿using System;
using System.ComponentModel.DataAnnotations;

namespace Super_Market.Core.Models
{
    public class AchatDetail : BaseEntity
    {
        [Required]
        public int AchatId { get; set; }

        public virtual Achat Achat { get; set; }

        [Required]
        public int ProduitId { get; set; }

        public virtual Produit Produit { get; set; }

        [Required]
        public decimal QuantiteCommandee { get; set; }

        public decimal QuantiteRecue { get; set; } = 0;

        [Required]
        public decimal PrixUnitaire { get; set; }

        public decimal Remise { get; set; } = 0;

        public decimal TauxTVA { get; set; } = 0;

        public decimal MontantTVA { get; set; } = 0;

        [Required]
        public decimal SousTotal { get; set; }

        [StringLength(200)]
        public string Notes { get; set; }
    }
}

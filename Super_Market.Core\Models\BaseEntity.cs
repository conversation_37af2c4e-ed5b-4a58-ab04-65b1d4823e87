using System;
using System.ComponentModel.DataAnnotations;

namespace Super_Market.Core.Models
{
    public abstract class BaseEntity
    {
        [Key]
        public int Id { get; set; }

        [Required]
        public DateTime DateCreation { get; set; } = DateTime.Now;

        public DateTime? DateModification { get; set; }

        [Required]
        [StringLength(50)]
        public string UtilisateurCreation { get; set; }

        [StringLength(50)]
        public string UtilisateurModification { get; set; }

        public bool EstActif { get; set; } = true;

        public bool EstSupprime { get; set; } = false;
    }
}

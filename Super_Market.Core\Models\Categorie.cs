﻿using System;
using System.ComponentModel.DataAnnotations;
using System.Collections.Generic;

namespace Super_Market.Core.Models
{
    public class Categorie : BaseEntity
    {
        [Required]
        [StringLength(100)]
        public string Nom { get; set; }

        [StringLength(500)]
        public string Description { get; set; }

        [StringLength(10)]
        public string Code { get; set; }

        public int? CategorieParentId { get; set; }

        public virtual Categorie CategorieParent { get; set; }

        public virtual ICollection<Categorie> SousCategories { get; set; }

        public virtual ICollection<Produit> Produits { get; set; }

        public Categorie()
        {
            SousCategories = new HashSet<Categorie>();
            Produits = new HashSet<Produit>();
        }
    }
}

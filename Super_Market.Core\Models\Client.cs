﻿using System;
using System.ComponentModel.DataAnnotations;
using System.Collections.Generic;

namespace Super_Market.Core.Models
{
    public class Client : BaseEntity
    {
        [Required]
        [StringLength(100)]
        public string Nom { get; set; }

        [Required]
        [StringLength(100)]
        public string Prenom { get; set; }

        [StringLength(100)]
        public string RaisonSociale { get; set; } // Pour les entreprises

        [StringLength(20)]
        public string NumeroRegistreCommerce { get; set; }

        [StringLength(20)]
        public string NumeroIdentificationFiscale { get; set; }

        [Required]
        [EmailAddress]
        [StringLength(150)]
        public string Email { get; set; }

        [StringLength(20)]
        public string Telephone { get; set; }

        [StringLength(20)]
        public string TelephoneSecondaire { get; set; }

        [StringLength(500)]
        public string Adresse { get; set; }

        [StringLength(100)]
        public string Ville { get; set; }

        [StringLength(10)]
        public string CodePostal { get; set; }

        [StringLength(100)]
        public string Pays { get; set; } = "Algérie";

        public DateTime? DateNaissance { get; set; }

        public bool EstEntreprise { get; set; } = false;

        public decimal LimiteCredit { get; set; } = 0;

        public decimal SoldeCompte { get; set; } = 0;

        public int PointsFidelite { get; set; } = 0;

        [StringLength(500)]
        public string Notes { get; set; }

        public virtual ICollection<Vente> Ventes { get; set; }

        public Client()
        {
            Ventes = new HashSet<Vente>();
        }
    }
}

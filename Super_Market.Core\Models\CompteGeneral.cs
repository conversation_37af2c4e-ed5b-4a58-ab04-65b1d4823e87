﻿using System;
using System.ComponentModel.DataAnnotations;
using System.Collections.Generic;

namespace Super_Market.Core.Models
{
    public class CompteGeneral : BaseEntity
    {
        [Required]
        [StringLength(20)]
        public string NumeroCompte { get; set; }

        [Required]
        [StringLength(150)]
        public string NomCompte { get; set; }

        [StringLength(500)]
        public string Description { get; set; }

        [Required]
        [StringLength(50)]
        public string TypeCompte { get; set; } // Actif, Passif, Charge, Produit

        [StringLength(50)]
        public string SousTypeCompte { get; set; }

        public int? CompteParentId { get; set; }

        public virtual CompteGeneral CompteParent { get; set; }

        public decimal SoldeDebit { get; set; } = 0;

        public decimal SoldeCredit { get; set; } = 0;

        public bool EstCompteCollectif { get; set; } = false;

        public bool EstCompteTiers { get; set; } = false;

        [StringLength(500)]
        public string Notes { get; set; }

        public virtual ICollection<CompteGeneral> SousComptes { get; set; }

        public virtual ICollection<Revenu> Revenus { get; set; }

        public virtual ICollection<Depense> Depenses { get; set; }

        // Propriété calculée
        public decimal SoldeNet => SoldeDebit - SoldeCredit;

        public CompteGeneral()
        {
            SousComptes = new HashSet<CompteGeneral>();
            Revenus = new HashSet<Revenu>();
            Depenses = new HashSet<Depense>();
        }
    }
}

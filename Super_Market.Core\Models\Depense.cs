﻿using System;
using System.ComponentModel.DataAnnotations;

namespace Super_Market.Core.Models
{
    public class Depense : BaseEntity
    {
        [Required]
        [StringLength(150)]
        public string Description { get; set; }

        [Required]
        public decimal Montant { get; set; }

        [Required]
        public DateTime DateDepense { get; set; } = DateTime.Now;

        [Required]
        public int CompteGeneralId { get; set; }

        public virtual CompteGeneral CompteGeneral { get; set; }

        [StringLength(50)]
        public string TypeDepense { get; set; } // Achat, Salaire, Loyer, Autre

        [StringLength(100)]
        public string Reference { get; set; } // Numéro de facture, reçu, etc.

        public int? AchatId { get; set; }

        public virtual Achat Achat { get; set; }

        public int? FournisseurId { get; set; }

        public virtual Fournisseur Fournisseur { get; set; }

        [Required]
        public int UtilisateurId { get; set; }

        public virtual Utilisateur Utilisateur { get; set; }

        [StringLength(500)]
        public string Notes { get; set; }
    }
}

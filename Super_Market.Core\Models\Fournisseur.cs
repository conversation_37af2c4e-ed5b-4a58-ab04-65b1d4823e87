﻿using System;
using System.ComponentModel.DataAnnotations;
using System.Collections.Generic;

namespace Super_Market.Core.Models
{
    public class Fournisseur : BaseEntity
    {
        [Required]
        [StringLength(150)]
        public string RaisonSociale { get; set; }

        [StringLength(100)]
        public string NomContact { get; set; }

        [StringLength(100)]
        public string PrenomContact { get; set; }

        [Required]
        [StringLength(20)]
        public string NumeroRegistreCommerce { get; set; }

        [StringLength(20)]
        public string NumeroIdentificationFiscale { get; set; }

        [Required]
        [EmailAddress]
        [StringLength(150)]
        public string Email { get; set; }

        [StringLength(20)]
        public string Telephone { get; set; }

        [StringLength(20)]
        public string Fax { get; set; }

        [StringLength(500)]
        public string Adresse { get; set; }

        [StringLength(100)]
        public string Ville { get; set; }

        [StringLength(10)]
        public string CodePostal { get; set; }

        [StringLength(100)]
        public string Pays { get; set; } = "Algérie";

        [StringLength(100)]
        public string SiteWeb { get; set; }

        public int DelaiPaiement { get; set; } = 30; // en jours

        public decimal LimiteCredit { get; set; } = 0;

        public decimal SoldeCompte { get; set; } = 0;

        [StringLength(500)]
        public string ConditionsCommerciales { get; set; }

        [StringLength(500)]
        public string Notes { get; set; }

        public virtual ICollection<Achat> Achats { get; set; }

        public Fournisseur()
        {
            Achats = new HashSet<Achat>();
        }
    }
}

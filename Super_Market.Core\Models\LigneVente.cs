﻿using System;
using System.ComponentModel.DataAnnotations;

namespace Super_Market.Core.Models
{
    // Cette classe peut être utilisée pour les ventes temporaires (panier)
    // avant la validation finale dans VenteDetail
    public class LigneVente : BaseEntity
    {
        [Required]
        public int ProduitId { get; set; }

        public virtual Produit Produit { get; set; }

        [Required]
        public decimal Quantite { get; set; }

        [Required]
        public decimal PrixUnitaire { get; set; }

        public decimal Remise { get; set; } = 0;

        public decimal TauxTVA { get; set; } = 0;

        [Required]
        public decimal SousTotal { get; set; }

        [StringLength(100)]
        public string SessionId { get; set; } // Pour identifier la session de vente

        [Required]
        public int UtilisateurId { get; set; }

        public virtual Utilisateur Utilisateur { get; set; }

        [StringLength(200)]
        public string Notes { get; set; }

        public bool EstTemporaire { get; set; } = true;
    }
}

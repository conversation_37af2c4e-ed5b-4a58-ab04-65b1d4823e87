﻿using System;
using System.ComponentModel.DataAnnotations;
using Super_Market.Core.Enums;

namespace Super_Market.Core.Models
{
    public class MouvementStock : BaseEntity
    {
        [Required]
        public int ProduitId { get; set; }

        public virtual Produit Produit { get; set; }

        [Required]
        public TypeMouvement TypeMouvement { get; set; }

        [Required]
        public decimal Quantite { get; set; }

        public decimal QuantiteAvant { get; set; }

        public decimal QuantiteApres { get; set; }

        [Required]
        public DateTime DateMouvement { get; set; } = DateTime.Now;

        [StringLength(200)]
        public string Reference { get; set; } // Numéro de facture, bon de livraison, etc.

        [StringLength(500)]
        public string Motif { get; set; }

        [Required]
        public int UtilisateurId { get; set; }

        public virtual Utilisateur Utilisateur { get; set; }

        public decimal? CoutUnitaire { get; set; }

        public decimal? ValeurMouvement { get; set; }

        [StringLength(500)]
        public string Notes { get; set; }
    }
}

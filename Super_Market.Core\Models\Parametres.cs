﻿using System;
using System.ComponentModel.DataAnnotations;

namespace Super_Market.Core.Models
{
    public class Parametres : BaseEntity
    {
        [Required]
        [StringLength(150)]
        public string NomEntreprise { get; set; }

        [StringLength(500)]
        public string AdresseEntreprise { get; set; }

        [StringLength(20)]
        public string TelephoneEntreprise { get; set; }

        [StringLength(20)]
        public string FaxEntreprise { get; set; }

        [EmailAddress]
        [StringLength(150)]
        public string EmailEntreprise { get; set; }

        [StringLength(100)]
        public string SiteWebEntreprise { get; set; }

        [StringLength(20)]
        public string NumeroRegistreCommerce { get; set; }

        [StringLength(20)]
        public string NumeroIdentificationFiscale { get; set; }

        [StringLength(20)]
        public string NumeroArticleImposition { get; set; }

        [StringLength(200)]
        public string LogoPath { get; set; }

        [Required]
        [StringLength(10)]
        public string DeviseDefaut { get; set; } = "DZD";

        public decimal TauxTVADefaut { get; set; } = 19;

        public int DelaiPaiementDefaut { get; set; } = 30;

        [StringLength(50)]
        public string FormatFacture { get; set; } = "FAC-{0:000000}";

        [StringLength(50)]
        public string FormatCommande { get; set; } = "CMD-{0:000000}";

        public bool ActiverGestionStock { get; set; } = true;

        public bool ActiverAlertesStock { get; set; } = true;

        public bool ActiverSauvegarde { get; set; } = true;

        public int FrequenceSauvegarde { get; set; } = 24; // en heures

        [StringLength(500)]
        public string CheminSauvegarde { get; set; }

        [StringLength(500)]
        public string MessageFacture { get; set; }

        [StringLength(500)]
        public string ConditionsVente { get; set; }

        public bool ImprimerAutomatiquement { get; set; } = false;

        [StringLength(100)]
        public string ImprimanteDefaut { get; set; }

        public bool ActiverPointsFidelite { get; set; } = false;

        public decimal TauxPointsFidelite { get; set; } = 1; // 1 point par 100 DZD

        [StringLength(500)]
        public string Notes { get; set; }
    }
}

﻿using System;
using System.ComponentModel.DataAnnotations;
using System.Collections.Generic;

namespace Super_Market.Core.Models
{
    public class Produit : BaseEntity
    {
        [Required]
        [StringLength(100)]
        public string Nom { get; set; }

        [StringLength(500)]
        public string Description { get; set; }

        [Required]
        [StringLength(50)]
        public string CodeBarre { get; set; }

        [StringLength(20)]
        public string CodeInterne { get; set; }

        [Required]
        public int CategorieId { get; set; }

        public virtual Categorie Categorie { get; set; }

        [Required]
        public decimal PrixAchat { get; set; }

        [Required]
        public decimal PrixVente { get; set; }

        public decimal? PrixPromotion { get; set; }

        public DateTime? DateDebutPromotion { get; set; }

        public DateTime? DateFinPromotion { get; set; }

        [Required]
        [StringLength(20)]
        public string Unite { get; set; } // Pièce, Kg, Litre, etc.

        public decimal? Poids { get; set; }

        public decimal? Volume { get; set; }

        [StringLength(100)]
        public string Marque { get; set; }

        public DateTime? DateExpiration { get; set; }

        public int StockMinimum { get; set; } = 0;

        public int StockMaximum { get; set; } = 1000;

        public decimal? TVA { get; set; } = 19; // TVA en pourcentage

        [StringLength(200)]
        public string ImagePath { get; set; }

        [StringLength(500)]
        public string Notes { get; set; }

        public virtual ICollection<Stock> Stocks { get; set; }

        public virtual ICollection<VenteDetail> VenteDetails { get; set; }

        public virtual ICollection<AchatDetail> AchatDetails { get; set; }

        public virtual ICollection<MouvementStock> MouvementStocks { get; set; }

        public Produit()
        {
            Stocks = new HashSet<Stock>();
            VenteDetails = new HashSet<VenteDetail>();
            AchatDetails = new HashSet<AchatDetail>();
            MouvementStocks = new HashSet<MouvementStock>();
        }
    }
}

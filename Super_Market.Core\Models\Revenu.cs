﻿using System;
using System.ComponentModel.DataAnnotations;

namespace Super_Market.Core.Models
{
    public class Revenu : BaseEntity
    {
        [Required]
        [StringLength(150)]
        public string Description { get; set; }

        [Required]
        public decimal Montant { get; set; }

        [Required]
        public DateTime DateRevenu { get; set; } = DateTime.Now;

        [Required]
        public int CompteGeneralId { get; set; }

        public virtual CompteGeneral CompteGeneral { get; set; }

        [StringLength(50)]
        public string TypeRevenu { get; set; } // Vente, Service, Autre

        [StringLength(100)]
        public string Reference { get; set; } // Numéro de facture, reçu, etc.

        public int? VenteId { get; set; }

        public virtual Vente Vente { get; set; }

        [Required]
        public int UtilisateurId { get; set; }

        public virtual Utilisateur Utilisateur { get; set; }

        [StringLength(500)]
        public string Notes { get; set; }
    }
}

﻿using System;
using System.ComponentModel.DataAnnotations;
using Super_Market.Core.Enums;

namespace Super_Market.Core.Models
{
    public class Utilisateur : BaseEntity
    {
        [Required]
        [StringLength(50)]
        public string NomUtilisateur { get; set; }

        [Required]
        [StringLength(100)]
        public string MotDePasse { get; set; }

        [Required]
        [StringLength(100)]
        public string Nom { get; set; }

        [Required]
        [StringLength(100)]
        public string Prenom { get; set; }

        [Required]
        [EmailAddress]
        [StringLength(150)]
        public string Email { get; set; }

        [StringLength(20)]
        public string Telephone { get; set; }

        [Required]
        public TypeUtilisateur TypeUtilisateur { get; set; }

        public DateTime? DerniereConnexion { get; set; }

        public bool EstConnecte { get; set; } = false;

        [StringLength(500)]
        public string Adresse { get; set; }

        public decimal? Salaire { get; set; }

        public DateTime? DateEmbauche { get; set; }

        [StringLength(200)]
        public string Notes { get; set; }
    }
}

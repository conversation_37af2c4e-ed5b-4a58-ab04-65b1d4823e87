﻿using System;
using System.ComponentModel.DataAnnotations;
using System.Collections.Generic;
using Super_Market.Core.Enums;

namespace Super_Market.Core.Models
{
    public class Vente : BaseEntity
    {
        [Required]
        [StringLength(20)]
        public string NumeroFacture { get; set; }

        [Required]
        public DateTime DateVente { get; set; } = DateTime.Now;

        public int? ClientId { get; set; }

        public virtual Client Client { get; set; }

        [Required]
        public int UtilisateurId { get; set; }

        public virtual Utilisateur Utilisateur { get; set; }

        [Required]
        public decimal SousTotal { get; set; }

        public decimal Remise { get; set; } = 0;

        public decimal MontantTVA { get; set; } = 0;

        [Required]
        public decimal Total { get; set; }

        public decimal MontantPaye { get; set; } = 0;

        public decimal MontantRendu { get; set; } = 0;

        [Required]
        public ModePaiement ModePaiement { get; set; }

        [Required]
        public StatutVente Statut { get; set; } = StatutVente.EnCours;

        [StringLength(500)]
        public string Notes { get; set; }

        public bool EstFacturee { get; set; } = false;

        public DateTime? DateFacturation { get; set; }

        public virtual ICollection<VenteDetail> VenteDetails { get; set; }

        public Vente()
        {
            VenteDetails = new HashSet<VenteDetail>();
        }
    }
}

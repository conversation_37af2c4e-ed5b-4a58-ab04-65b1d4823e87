<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{CE1AEE59-81E2-4971-A3F5-4E62246B8F29}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Super_Market.Core</RootNamespace>
    <AssemblyName>Super_Market.Core</AssemblyName>
    <TargetFrameworkVersion>v4.6.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Enums\ModePaiement.cs" />
    <Compile Include="Enums\StatutAchat.cs" />
    <Compile Include="Enums\StatutVente.cs" />
    <Compile Include="Enums\TypeMouvement.cs" />
    <Compile Include="Enums\TypeUtilisateur.cs" />
    <Compile Include="Models\Achat.cs" />
    <Compile Include="Models\AchatDetail.cs" />
    <Compile Include="Models\BaseEntity.cs" />
    <Compile Include="Models\Categorie.cs" />
    <Compile Include="Models\Client.cs" />
    <Compile Include="Models\CompteGeneral.cs" />
    <Compile Include="Models\Depense.cs" />
    <Compile Include="Models\Fournisseur.cs" />
    <Compile Include="Models\LigneVente.cs" />
    <Compile Include="Models\MouvementStock.cs" />
    <Compile Include="Models\Parametres.cs" />
    <Compile Include="Models\Produit.cs" />
    <Compile Include="Models\Revenu.cs" />
    <Compile Include="Models\Stock.cs" />
    <Compile Include="Models\Utilisateur.cs" />
    <Compile Include="Models\Vente.cs" />
    <Compile Include="Models\VenteDetail.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>
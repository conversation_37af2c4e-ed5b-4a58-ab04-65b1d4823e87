﻿using System;
using System.Configuration;
using System.Data;
using MySql.Data.MySqlClient;

namespace Super_Market.Data
{
    public class DatabaseConfig
    {
        private static string _connectionString;

        public static string ConnectionString
        {
            get
            {
                if (string.IsNullOrEmpty(_connectionString))
                {
                    _connectionString = ConfigurationManager.ConnectionStrings["DefaultConnection"]?.ConnectionString
                        ?? "Server=localhost;Database=zinstore_db;Uid=root;Pwd=;";
                }
                return _connectionString;
            }
            set
            {
                _connectionString = value;
            }
        }

        public static IDbConnection CreateConnection()
        {
            return new MySqlConnection(ConnectionString);
        }

        public static bool TestConnection()
        {
            try
            {
                using (var connection = CreateConnection())
                {
                    connection.Open();
                    return true;
                }
            }
            catch
            {
                return false;
            }
        }
    }
}

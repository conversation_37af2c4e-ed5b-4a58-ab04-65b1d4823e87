using System;
using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;
using Dapper;
using Super_Market.Data.Interfaces;

namespace Super_Market.Data.Repositories
{
    public abstract class BaseRepository<T> : IRepository<T> where T : class
    {
        protected readonly string _tableName;
        protected readonly string _keyColumn;
        
        protected BaseRepository(string tableName, string keyColumn = "Id")
        {
            _tableName = tableName;
            _keyColumn = keyColumn;
        }
        
        protected IDbConnection CreateConnection()
        {
            return DatabaseConfig.CreateConnection();
        }
        
        public virtual async Task<IEnumerable<T>> GetAllAsync()
        {
            using (var connection = CreateConnection())
            {
                var sql = $"SELECT * FROM {_tableName} WHERE EstSupprime = 0 ORDER BY {_keyColumn}";
                return await connection.QueryAsync<T>(sql);
            }
        }
        
        public virtual async Task<T> GetByIdAsync(int id)
        {
            using (var connection = CreateConnection())
            {
                var sql = $"SELECT * FROM {_tableName} WHERE {_keyColumn} = @Id AND EstSupprime = 0";
                return await connection.QueryFirstOrDefaultAsync<T>(sql, new { Id = id });
            }
        }
        
        public abstract Task<int> AddAsync(T entity);
        
        public abstract Task<bool> UpdateAsync(T entity);
        
        public virtual async Task<bool> DeleteAsync(int id)
        {
            using (var connection = CreateConnection())
            {
                var sql = $"UPDATE {_tableName} SET EstSupprime = 1, DateModification = @DateModification WHERE {_keyColumn} = @Id";
                var result = await connection.ExecuteAsync(sql, new { Id = id, DateModification = DateTime.Now });
                return result > 0;
            }
        }
        
        public virtual async Task<bool> ExistsAsync(int id)
        {
            using (var connection = CreateConnection())
            {
                var sql = $"SELECT COUNT(1) FROM {_tableName} WHERE {_keyColumn} = @Id AND EstSupprime = 0";
                var count = await connection.QuerySingleAsync<int>(sql, new { Id = id });
                return count > 0;
            }
        }
        
        public abstract Task<IEnumerable<T>> SearchAsync(string searchTerm);
        
        public virtual async Task<int> CountAsync()
        {
            using (var connection = CreateConnection())
            {
                var sql = $"SELECT COUNT(*) FROM {_tableName} WHERE EstSupprime = 0";
                return await connection.QuerySingleAsync<int>(sql);
            }
        }
        
        protected async Task<bool> HardDeleteAsync(int id)
        {
            using (var connection = CreateConnection())
            {
                var sql = $"DELETE FROM {_tableName} WHERE {_keyColumn} = @Id";
                var result = await connection.ExecuteAsync(sql, new { Id = id });
                return result > 0;
            }
        }
    }
}

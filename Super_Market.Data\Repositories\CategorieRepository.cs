using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dapper;
using Super_Market.Core.Models;

namespace Super_Market.Data.Repositories
{
    public class CategorieRepository : BaseRepository<Categorie>
    {
        public CategorieRepository() : base("Categories")
        {
        }
        
        public override async Task<int> AddAsync(Categorie entity)
        {
            using var connection = CreateConnection();
            var sql = @"
                INSERT INTO Categories 
                (Nom, Description, Code, CategorieParentId, DateCreation, UtilisateurCreation, EstActif)
                VALUES 
                (@Nom, @Description, @Code, @CategorieParentId, @DateCreation, @UtilisateurCreation, @EstActif);
                SELECT LAST_INSERT_ID();";
            
            return await connection.QuerySingleAsync<int>(sql, entity);
        }
        
        public override async Task<bool> UpdateAsync(Categorie entity)
        {
            using var connection = CreateConnection();
            var sql = @"
                UPDATE Categories SET 
                    Nom = @Nom,
                    Description = @Description,
                    Code = @Code,
                    CategorieParentId = @CategorieParentId,
                    DateModification = @DateModification,
                    UtilisateurModification = @UtilisateurModification,
                    EstActif = @EstActif
                WHERE Id = @Id";
            
            var result = await connection.ExecuteAsync(sql, entity);
            return result > 0;
        }
        
        public override async Task<IEnumerable<Categorie>> SearchAsync(string searchTerm)
        {
            using var connection = CreateConnection();
            var sql = @"
                SELECT c.*, cp.Nom as NomParent 
                FROM Categories c
                LEFT JOIN Categories cp ON c.CategorieParentId = cp.Id
                WHERE c.EstSupprime = 0 
                AND (c.Nom LIKE @SearchTerm 
                     OR c.Description LIKE @SearchTerm 
                     OR c.Code LIKE @SearchTerm)
                ORDER BY c.Nom";
            
            return await connection.QueryAsync<Categorie>(sql, new { SearchTerm = $"%{searchTerm}%" });
        }
        
        public async Task<IEnumerable<Categorie>> GetCategoriesPrincipalesAsync()
        {
            using var connection = CreateConnection();
            var sql = "SELECT * FROM Categories WHERE CategorieParentId IS NULL AND EstSupprime = 0 ORDER BY Nom";
            return await connection.QueryAsync<Categorie>(sql);
        }
        
        public async Task<IEnumerable<Categorie>> GetSousCategoriesAsync(int categorieParentId)
        {
            using var connection = CreateConnection();
            var sql = "SELECT * FROM Categories WHERE CategorieParentId = @CategorieParentId AND EstSupprime = 0 ORDER BY Nom";
            return await connection.QueryAsync<Categorie>(sql, new { CategorieParentId = categorieParentId });
        }
        
        public async Task<bool> HasSousCategoriesAsync(int categorieId)
        {
            using var connection = CreateConnection();
            var sql = "SELECT COUNT(1) FROM Categories WHERE CategorieParentId = @CategorieId AND EstSupprime = 0";
            var count = await connection.QuerySingleAsync<int>(sql, new { CategorieId = categorieId });
            return count > 0;
        }
        
        public async Task<bool> HasProduitsAsync(int categorieId)
        {
            using var connection = CreateConnection();
            var sql = "SELECT COUNT(1) FROM Produits WHERE CategorieId = @CategorieId AND EstSupprime = 0";
            var count = await connection.QuerySingleAsync<int>(sql, new { CategorieId = categorieId });
            return count > 0;
        }
        
        public async Task<bool> ExistsByCodeAsync(string code, int? excludeId = null)
        {
            using var connection = CreateConnection();
            var sql = "SELECT COUNT(1) FROM Categories WHERE Code = @Code AND EstSupprime = 0";
            
            if (excludeId.HasValue)
            {
                sql += " AND Id != @ExcludeId";
                var count = await connection.QuerySingleAsync<int>(sql, new { Code = code, ExcludeId = excludeId.Value });
                return count > 0;
            }
            else
            {
                var count = await connection.QuerySingleAsync<int>(sql, new { Code = code });
                return count > 0;
            }
        }
        
        public async Task<IEnumerable<Categorie>> GetHierarchieCompleteAsync()
        {
            using var connection = CreateConnection();
            var sql = @"
                WITH RECURSIVE CategorieHierarchy AS (
                    SELECT Id, Nom, Description, Code, CategorieParentId, 0 as Niveau, 
                           CAST(Nom AS CHAR(1000)) as CheminComplet
                    FROM Categories 
                    WHERE CategorieParentId IS NULL AND EstSupprime = 0
                    
                    UNION ALL
                    
                    SELECT c.Id, c.Nom, c.Description, c.Code, c.CategorieParentId, 
                           ch.Niveau + 1, 
                           CONCAT(ch.CheminComplet, ' > ', c.Nom)
                    FROM Categories c
                    INNER JOIN CategorieHierarchy ch ON c.CategorieParentId = ch.Id
                    WHERE c.EstSupprime = 0
                )
                SELECT * FROM CategorieHierarchy ORDER BY CheminComplet";
            
            return await connection.QueryAsync<Categorie>(sql);
        }
    }
}

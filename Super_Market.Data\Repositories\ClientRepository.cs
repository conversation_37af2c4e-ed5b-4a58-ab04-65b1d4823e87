using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dapper;
using Super_Market.Core.Models;

namespace Super_Market.Data.Repositories
{
    public class ClientRepository : BaseRepository<Client>
    {
        public ClientRepository() : base("Clients")
        {
        }
        
        public override async Task<int> AddAsync(Client entity)
        {
            using (var connection = CreateConnection())
            {
                var sql = @"
                INSERT INTO Clients 
                (Nom, Prenom, RaisonSociale, NumeroRegistreCommerce, NumeroIdentificationFiscale, 
                 Email, Telephone, TelephoneSecondaire, Adresse, Ville, CodePostal, Pays, 
                 DateNaissance, EstEntreprise, LimiteCredit, SoldeCompte, PointsFidelite, Notes,
                 DateCreation, UtilisateurCreation, EstActif)
                VALUES 
                (@Nom, @Prenom, @RaisonSociale, @NumeroRegistreCommerce, @NumeroIdentificationFiscale,
                 @Email, @Telephone, @TelephoneSecondaire, @Adresse, @Ville, @CodePostal, @Pays,
                 @DateNaissance, @EstEntreprise, @LimiteCredit, @SoldeCompte, @PointsFidelite, @Notes,
                 @DateCreation, @UtilisateurCreation, @EstActif);
                SELECT LAST_INSERT_ID();";
            
                return await connection.QuerySingleAsync<int>(sql, entity);
            }
        }
        
        public override async Task<bool> UpdateAsync(Client entity)
        {
            using (var connection = CreateConnection())
            {
            var sql = @"
                UPDATE Clients SET 
                    Nom = @Nom,
                    Prenom = @Prenom,
                    RaisonSociale = @RaisonSociale,
                    NumeroRegistreCommerce = @NumeroRegistreCommerce,
                    NumeroIdentificationFiscale = @NumeroIdentificationFiscale,
                    Email = @Email,
                    Telephone = @Telephone,
                    TelephoneSecondaire = @TelephoneSecondaire,
                    Adresse = @Adresse,
                    Ville = @Ville,
                    CodePostal = @CodePostal,
                    Pays = @Pays,
                    DateNaissance = @DateNaissance,
                    EstEntreprise = @EstEntreprise,
                    LimiteCredit = @LimiteCredit,
                    SoldeCompte = @SoldeCompte,
                    PointsFidelite = @PointsFidelite,
                    Notes = @Notes,
                    DateModification = @DateModification,
                    UtilisateurModification = @UtilisateurModification,
                    EstActif = @EstActif
                WHERE Id = @Id";
            
                var result = await connection.ExecuteAsync(sql, entity);
                return result > 0;
            }
        }
        
        public override async Task<IEnumerable<Client>> SearchAsync(string searchTerm)
        {
            using (var connection = CreateConnection())
            {
                var sql = @"
                    SELECT * FROM Clients
                    WHERE EstSupprime = 0
                    AND (Nom LIKE @SearchTerm
                         OR Prenom LIKE @SearchTerm
                         OR RaisonSociale LIKE @SearchTerm
                         OR Email LIKE @SearchTerm
                         OR Telephone LIKE @SearchTerm)
                    ORDER BY Nom, Prenom";

                return await connection.QueryAsync<Client>(sql, new { SearchTerm = $"%{searchTerm}%" });
            }
        }
        
        public async Task<Client> GetByEmailAsync(string email)
        {
            using (var connection = CreateConnection())
            {
                var sql = "SELECT * FROM Clients WHERE Email = @Email AND EstSupprime = 0";
                return await connection.QueryFirstOrDefaultAsync<Client>(sql, new { Email = email });
            }
        }
        
        public async Task<Client> GetByTelephoneAsync(string telephone)
        {
            using (var connection = CreateConnection())
            {
                var sql = "SELECT * FROM Clients WHERE Telephone = @Telephone AND EstSupprime = 0";
                return await connection.QueryFirstOrDefaultAsync<Client>(sql, new { Telephone = telephone });
            }
        }
        
        public async Task<IEnumerable<Client>> GetClientsEntrepriseAsync()
        {
            using (var connection = CreateConnection())
            {
                var sql = "SELECT * FROM Clients WHERE EstEntreprise = 1 AND EstSupprime = 0 ORDER BY RaisonSociale";
                return await connection.QueryAsync<Client>(sql);
            }
        }
        
        public async Task<IEnumerable<Client>> GetClientsParticuliersAsync()
        {
            using (var connection = CreateConnection())
            {
                var sql = "SELECT * FROM Clients WHERE EstEntreprise = 0 AND EstSupprime = 0 ORDER BY Nom, Prenom";
                return await connection.QueryAsync<Client>(sql);
            }
        }
        
        public async Task<bool> UpdateSoldeAsync(int clientId, decimal nouveauSolde)
        {
            using (var connection = CreateConnection())
            {
                var sql = "UPDATE Clients SET SoldeCompte = @NouveauSolde WHERE Id = @ClientId";
                var result = await connection.ExecuteAsync(sql, new { ClientId = clientId, NouveauSolde = nouveauSolde });
                return result > 0;
            }
        }
        
        public async Task<bool> UpdatePointsFideliteAsync(int clientId, int nouveauxPoints)
        {
            using (var connection = CreateConnection())
            {
                var sql = "UPDATE Clients SET PointsFidelite = @NouveauxPoints WHERE Id = @ClientId";
                var result = await connection.ExecuteAsync(sql, new { ClientId = clientId, NouveauxPoints = nouveauxPoints });
                return result > 0;
            }
        }
        
        public async Task<IEnumerable<Client>> GetClientsFidelesAsync(int pointsMinimum = 100)
        {
            using (var connection = CreateConnection())
            {
                var sql = "SELECT * FROM Clients WHERE PointsFidelite >= @PointsMinimum AND EstSupprime = 0 ORDER BY PointsFidelite DESC";
                return await connection.QueryAsync<Client>(sql, new { PointsMinimum = pointsMinimum });
            }
        }
        
        public async Task<decimal> GetChiffreAffairesClientAsync(int clientId, DateTime? dateDebut = null, DateTime? dateFin = null)
        {
            using (var connection = CreateConnection())
            {
            var sql = @"
                SELECT COALESCE(SUM(v.Total), 0) 
                FROM Ventes v 
                WHERE v.ClientId = @ClientId 
                AND v.EstSupprime = 0";
            
            if (dateDebut.HasValue)
                sql += " AND v.DateVente >= @DateDebut";
            if (dateFin.HasValue)
                sql += " AND v.DateVente <= @DateFin";
            
            return await connection.QuerySingleAsync<decimal>(sql, new { 
                ClientId = clientId, 
                DateDebut = dateDebut, 
                DateFin = dateFin 
            });
            }
        }
    }
}

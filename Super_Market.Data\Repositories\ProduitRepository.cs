using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dapper;
using Super_Market.Core.Models;

namespace Super_Market.Data.Repositories
{
    public class ProduitRepository : BaseRepository<Produit>
    {
        public ProduitRepository() : base("Produits")
        {
        }
        
        public override async Task<int> AddAsync(Produit entity)
        {
            using (var connection = CreateConnection())
            {
            var sql = @"
                INSERT INTO Produits 
                (Nom, Description, CodeBarre, CodeInterne, CategorieId, PrixAchat, PrixVente, 
                 PrixPromotion, DateDebutPromotion, DateFinPromotion, Unite, Poids, Volume, 
                 Marque, DateExpiration, StockMinimum, StockMaximum, TVA, ImagePath, Notes,
                 DateCreation, UtilisateurCreation, EstActif)
                VALUES 
                (@Nom, @Description, @CodeBarre, @CodeInterne, @CategorieId, @PrixAchat, @PrixVente,
                 @PrixPromotion, @DateDebutPromotion, @DateFinPromotion, @Unite, @Poids, @Volume,
                 @Marque, @DateExpiration, @StockMinimum, @StockMaximum, @TVA, @ImagePath, @Notes,
                 @DateCreation, @UtilisateurCreation, @EstActif);
                SELECT LAST_INSERT_ID();";
            
                return await connection.QuerySingleAsync<int>(sql, entity);
            }
        }
        
        public override async Task<bool> UpdateAsync(Produit entity)
        {
            using (var connection = CreateConnection())
            {
            var sql = @"
                UPDATE Produits SET 
                    Nom = @Nom,
                    Description = @Description,
                    CodeBarre = @CodeBarre,
                    CodeInterne = @CodeInterne,
                    CategorieId = @CategorieId,
                    PrixAchat = @PrixAchat,
                    PrixVente = @PrixVente,
                    PrixPromotion = @PrixPromotion,
                    DateDebutPromotion = @DateDebutPromotion,
                    DateFinPromotion = @DateFinPromotion,
                    Unite = @Unite,
                    Poids = @Poids,
                    Volume = @Volume,
                    Marque = @Marque,
                    DateExpiration = @DateExpiration,
                    StockMinimum = @StockMinimum,
                    StockMaximum = @StockMaximum,
                    TVA = @TVA,
                    ImagePath = @ImagePath,
                    Notes = @Notes,
                    DateModification = @DateModification,
                    UtilisateurModification = @UtilisateurModification,
                    EstActif = @EstActif
                WHERE Id = @Id";
            
                var result = await connection.ExecuteAsync(sql, entity);
                return result > 0;
            }
        }
        
        public override async Task<IEnumerable<Produit>> SearchAsync(string searchTerm)
        {
            using (var connection = CreateConnection())
            {
                var sql = @"
                    SELECT p.*, c.Nom as CategorieName
                    FROM Produits p
                    LEFT JOIN Categories c ON p.CategorieId = c.Id
                    WHERE p.EstSupprime = 0
                    AND (p.Nom LIKE @SearchTerm
                         OR p.Description LIKE @SearchTerm
                         OR p.CodeBarre LIKE @SearchTerm
                         OR p.CodeInterne LIKE @SearchTerm
                         OR p.Marque LIKE @SearchTerm)
                    ORDER BY p.Nom";

                return await connection.QueryAsync<Produit>(sql, new { SearchTerm = $"%{searchTerm}%" });
            }
        }
        
        public async Task<Produit> GetByCodeBarreAsync(string codeBarre)
        {
            using (var connection = CreateConnection())
            {
                var sql = "SELECT * FROM Produits WHERE CodeBarre = @CodeBarre AND EstSupprime = 0";
                return await connection.QueryFirstOrDefaultAsync<Produit>(sql, new { CodeBarre = codeBarre });
            }
        }
        
        public async Task<IEnumerable<Produit>> GetByCategorieAsync(int categorieId)
        {
            using (var connection = CreateConnection())
            {
                var sql = "SELECT * FROM Produits WHERE CategorieId = @CategorieId AND EstSupprime = 0 ORDER BY Nom";
                return await connection.QueryAsync<Produit>(sql, new { CategorieId = categorieId });
            }
        }
        
        public async Task<IEnumerable<Produit>> GetProduitsEnPromotionAsync()
        {
            using (var connection = CreateConnection())
            {
                var sql = @"
                    SELECT * FROM Produits
                    WHERE EstSupprime = 0
                    AND PrixPromotion IS NOT NULL
                    AND DateDebutPromotion <= @DateActuelle
                    AND DateFinPromotion >= @DateActuelle
                    ORDER BY Nom";

                return await connection.QueryAsync<Produit>(sql, new { DateActuelle = DateTime.Now });
            }
        }
        
        public async Task<IEnumerable<Produit>> GetProduitsStockFaibleAsync()
        {
            using (var connection = CreateConnection())
            {
                var sql = @"
                    SELECT p.*, s.QuantiteDisponible
                    FROM Produits p
                    INNER JOIN Stocks s ON p.Id = s.ProduitId
                    WHERE p.EstSupprime = 0
                    AND s.QuantiteDisponible <= p.StockMinimum
                    ORDER BY s.QuantiteDisponible";

                return await connection.QueryAsync<Produit>(sql);
            }
        }
        
        public async Task<bool> ExistsByCodeBarreAsync(string codeBarre, int? excludeId = null)
        {
            using (var connection = CreateConnection())
            {
                var sql = "SELECT COUNT(1) FROM Produits WHERE CodeBarre = @CodeBarre AND EstSupprime = 0";

                if (excludeId.HasValue)
                {
                    sql += " AND Id != @ExcludeId";
                    var count = await connection.QuerySingleAsync<int>(sql, new { CodeBarre = codeBarre, ExcludeId = excludeId.Value });
                    return count > 0;
                }
                else
                {
                    var count = await connection.QuerySingleAsync<int>(sql, new { CodeBarre = codeBarre });
                    return count > 0;
                }
            }
        }
    }
}

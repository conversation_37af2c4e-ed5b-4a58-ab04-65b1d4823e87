using Dapper;
using Super_Market.Core.Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Super_Market.Data.Repositories
{
    public class StockRepository : BaseRepository<Stock>
    {
        public StockRepository() : base("Stocks")
        {
        }

        public override async Task<int> AddAsync(Stock entity)
        {
            using (var connection = CreateConnection())
            {
                var sql = @"
                INSERT INTO Stocks 
                (ProduitId, QuantiteDisponible, QuantiteReservee, QuantiteMinimum, QuantiteMaximum,
                 Emplacement, DateDernierInventaire, CoutMoyenPondere, ValeurStock, Notes,
                 DateCreation, UtilisateurCreation, EstActif)
                VALUES 
                (@ProduitId, @QuantiteDisponible, @QuantiteReservee, @QuantiteMinimum, @QuantiteMaximum,
                 @Emplacement, @DateDernierInventaire, @CoutMoyenPondere, @ValeurStock, @Notes,
                 @DateCreation, @UtilisateurCreation, @EstActif);
                SELECT LAST_INSERT_ID();";

                return await connection.QuerySingleAsync<int>(sql, entity);
            }
        }

        public override async Task<bool> UpdateAsync(Stock entity)
        {
            using (var connection = CreateConnection())
            {
                var sql = @"
                UPDATE Stocks SET 
                    ProduitId = @ProduitId,
                    QuantiteDisponible = @QuantiteDisponible,
                    QuantiteReservee = @QuantiteReservee,
                    QuantiteMinimum = @QuantiteMinimum,
                    QuantiteMaximum = @QuantiteMaximum,
                    Emplacement = @Emplacement,
                    DateDernierInventaire = @DateDernierInventaire,
                    CoutMoyenPondere = @CoutMoyenPondere,
                    ValeurStock = @ValeurStock,
                    Notes = @Notes,
                    DateModification = @DateModification,
                    UtilisateurModification = @UtilisateurModification,
                    EstActif = @EstActif
                WHERE Id = @Id";

                var result = await connection.ExecuteAsync(sql, entity);
                return result > 0;
            }
        }

        public override async Task<IEnumerable<Stock>> SearchAsync(string searchTerm)
        {
            using (var connection = CreateConnection())
            {
                var sql = @"
                SELECT s.*, p.Nom as ProduitNom, p.CodeBarre
                FROM Stocks s
                INNER JOIN Produits p ON s.ProduitId = p.Id
                WHERE s.EstSupprime = 0 
                AND (p.Nom LIKE @SearchTerm 
                     OR p.CodeBarre LIKE @SearchTerm 
                     OR s.Emplacement LIKE @SearchTerm)
                ORDER BY p.Nom";

                return await connection.QueryAsync<Stock>(sql, new { SearchTerm = $"%{searchTerm}%" });
            }
        }

        public async Task<Stock> GetByProduitIdAsync(int produitId)
        {
            using (var connection = CreateConnection())
            {
                var sql = "SELECT * FROM Stocks WHERE ProduitId = @ProduitId AND EstSupprime = 0";
                return await connection.QueryFirstOrDefaultAsync<Stock>(sql, new { ProduitId = produitId });
            }
        }

        public async Task<IEnumerable<Stock>> GetStocksFaiblesAsync()
        {
            using (var connection = CreateConnection())
            {
                var sql = @"
                SELECT s.*, p.Nom as ProduitNom, p.CodeBarre
                FROM Stocks s
                INNER JOIN Produits p ON s.ProduitId = p.Id
                WHERE s.QuantiteDisponible <= s.QuantiteMinimum 
                AND s.EstSupprime = 0
                ORDER BY s.QuantiteDisponible";

                return await connection.QueryAsync<Stock>(sql);
            }
        }

        public async Task<IEnumerable<Stock>> GetSurstocksAsync()
        {
            using (var connection = CreateConnection())
            {
                var sql = @"
                SELECT s.*, p.Nom as ProduitNom, p.CodeBarre
                FROM Stocks s
                INNER JOIN Produits p ON s.ProduitId = p.Id
                WHERE s.QuantiteDisponible >= s.QuantiteMaximum 
                AND s.EstSupprime = 0
                ORDER BY s.QuantiteDisponible DESC";

                return await connection.QueryAsync<Stock>(sql);
            }
        }

        public async Task<bool> UpdateQuantiteAsync(int produitId, decimal nouvelleQuantite)
        {
            using (var connection = CreateConnection())
            {
                var sql = @"
                UPDATE Stocks SET 
                    QuantiteDisponible = @NouvelleQuantite,
                    DateModification = @DateModification
                WHERE ProduitId = @ProduitId";

                var result = await connection.ExecuteAsync(sql, new
                {
                    ProduitId = produitId,
                    NouvelleQuantite = nouvelleQuantite,
                    DateModification = DateTime.Now
                });
                return result > 0;
            }
        }

        public async Task<bool> AjusterStockAsync(int produitId, decimal quantiteAjustement, string motif)
        {
            using (var connection = CreateConnection())
            {
                using (var transaction = connection.BeginTransaction())
                {

                    try
                    {
                        // Mettre à jour le stock
                        var sqlStock = @"
                    UPDATE Stocks SET 
                        QuantiteDisponible = QuantiteDisponible + @QuantiteAjustement,
                        DateModification = @DateModification
                    WHERE ProduitId = @ProduitId";

                        await connection.ExecuteAsync(sqlStock, new
                        {
                            ProduitId = produitId,
                            QuantiteAjustement = quantiteAjustement,
                            DateModification = DateTime.Now
                        }, transaction);
                        // Enregistrer le mouvement de stock
                        var sqlMouvement = @"
                    INSERT INTO MouvementStocks 
                    (ProduitId, TypeMouvement, Quantite, DateMouvement, Motif, DateCreation, EstActif)
                    VALUES 
                    (@ProduitId, @TypeMouvement, @Quantite, @DateMouvement, @Motif, @DateCreation, 1)";

                        await connection.ExecuteAsync(sqlMouvement, new
                        {
                            ProduitId = produitId,
                            TypeMouvement = quantiteAjustement > 0 ? 1 : 2, // 1 = Entrée, 2 = Sortie
                            Quantite = Math.Abs(quantiteAjustement),
                            DateMouvement = DateTime.Now,
                            Motif = motif,
                            DateCreation = DateTime.Now
                        }, transaction);

                        transaction.Commit();
                        return true;
                    }
                    catch
                    {
                        transaction.Rollback();
                        return false;
                    }
                }
            }
        }

        public async Task<bool> ReserverStockAsync(int produitId, decimal quantite)
        {
            using (var connection = CreateConnection())
            {
                var sql = @"
                UPDATE Stocks SET 
                    QuantiteReservee = QuantiteReservee + @Quantite,
                    DateModification = @DateModification
                WHERE ProduitId = @ProduitId 
                AND QuantiteDisponible >= QuantiteReservee + @Quantite";

                var result = await connection.ExecuteAsync(sql, new
                {
                    ProduitId = produitId,
                    Quantite = quantite,
                    DateModification = DateTime.Now
                });
                return result > 0;
            }
        }

        public async Task<bool> LibererStockAsync(int produitId, decimal quantite)
        {
            using (var connection = CreateConnection())
            {
                var sql = @"
                UPDATE Stocks SET 
                    QuantiteReservee = GREATEST(0, QuantiteReservee - @Quantite),
                    DateModification = @DateModification
                WHERE ProduitId = @ProduitId";

                var result = await connection.ExecuteAsync(sql, new
                {
                    ProduitId = produitId,
                    Quantite = quantite,
                    DateModification = DateTime.Now
                });
                return result > 0;
            }
        }

        public async Task<decimal> GetValeurTotaleStockAsync()
        {
            using (var connection = CreateConnection())
            {
                var sql = "SELECT COALESCE(SUM(ValeurStock), 0) FROM Stocks WHERE EstSupprime = 0";
                return await connection.QuerySingleAsync<decimal>(sql);
            }
        }

        public async Task<int> GetNombreProduitsEnStockAsync()
        {
            using (var connection = CreateConnection())
            {
                var sql = "SELECT COUNT(*) FROM Stocks WHERE QuantiteDisponible > 0 AND EstSupprime = 0";
                return await connection.QuerySingleAsync<int>(sql);
            }
        }

        public async Task<int> GetNombreProduitsRuptureAsync()
        {
            using (var connection = CreateConnection())
            {
                var sql = "SELECT COUNT(*) FROM Stocks WHERE QuantiteDisponible <= QuantiteMinimum AND EstSupprime = 0";
                return await connection.QuerySingleAsync<int>(sql);
            }
        }

        public async Task<IEnumerable<dynamic>> GetStatistiquesStockAsync()
        {
            using (var connection = CreateConnection())
            {
                var sql = @"
                SELECT 
                    c.Nom as Categorie,
                    COUNT(s.Id) as NombreProduits,
                    SUM(s.QuantiteDisponible) as QuantiteTotale,
                    SUM(s.ValeurStock) as ValeurTotale,
                    COUNT(CASE WHEN s.QuantiteDisponible <= s.QuantiteMinimum THEN 1 END) as ProduitsEnRupture
                FROM Stocks s
                INNER JOIN Produits p ON s.ProduitId = p.Id
                INNER JOIN Categories c ON p.CategorieId = c.Id
                WHERE s.EstSupprime = 0
                GROUP BY c.Id, c.Nom
                ORDER BY c.Nom";

                return await connection.QueryAsync(sql);
            }
        }
    }
}

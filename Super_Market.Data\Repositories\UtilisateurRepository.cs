using Dapper;
using Super_Market.Core.Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Super_Market.Data.Repositories
{
    public class UtilisateurRepository : BaseRepository<Utilisateur>
    {
        public UtilisateurRepository() : base("Utilisateurs")
        {
        }

        public override async Task<int> AddAsync(Utilisateur entity)
        {
            using (var connection = CreateConnection())
            {
                var sql = @"
                INSERT INTO Utilisateurs 
                (NomUtilisateur, MotDePasse, Nom, Prenom, Email, Telephone, TypeUtilisateur, 
                 Adresse, Salaire, DateEmbauche, Notes, DateCreation, UtilisateurCreation, EstActif)
                VALUES 
                (@NomUtilisateur, @MotDePasse, @Nom, @Prenom, @Email, @Telephone, @TypeUtilisateur,
                 @Adresse, @Salaire, @DateEmbauche, @Notes, @DateCreation, @UtilisateurCreation, @EstActif);
                SELECT LAST_INSERT_ID();";

                return await connection.QuerySingleAsync<int>(sql, entity);
            }
        }

        public override async Task<bool> UpdateAsync(Utilisateur entity)
        {
            using (var connection = CreateConnection())
            {
                var sql = @"
                UPDATE Utilisateurs SET 
                    NomUtilisateur = @NomUtilisateur,
                    MotDePasse = @MotDePasse,
                    Nom = @Nom,
                    Prenom = @Prenom,
                    Email = @Email,
                    Telephone = @Telephone,
                    TypeUtilisateur = @TypeUtilisateur,
                    Adresse = @Adresse,
                    Salaire = @Salaire,
                    DateEmbauche = @DateEmbauche,
                    Notes = @Notes,
                    DateModification = @DateModification,
                    UtilisateurModification = @UtilisateurModification,
                    EstActif = @EstActif
                WHERE Id = @Id";

                var result = await connection.ExecuteAsync(sql, entity);
                return result > 0;
            }
        }

        public override async Task<IEnumerable<Utilisateur>> SearchAsync(string searchTerm)
        {
            using (var connection = CreateConnection())
            {
                var sql = @"
                SELECT * FROM Utilisateurs 
                WHERE EstSupprime = 0 
                AND (NomUtilisateur LIKE @SearchTerm 
                     OR Nom LIKE @SearchTerm 
                     OR Prenom LIKE @SearchTerm 
                     OR Email LIKE @SearchTerm)
                ORDER BY Nom, Prenom";

                return await connection.QueryAsync<Utilisateur>(sql, new { SearchTerm = $"%{searchTerm}%" });
            }
        }

        public async Task<Utilisateur> GetByNomUtilisateurAsync(string nomUtilisateur)
        {
            using (var connection = CreateConnection())
            {
                var sql = "SELECT * FROM Utilisateurs WHERE NomUtilisateur = @NomUtilisateur AND EstSupprime = 0";
                return await connection.QueryFirstOrDefaultAsync<Utilisateur>(sql, new { NomUtilisateur = nomUtilisateur });
            }
        }

        public async Task<bool> ValidateLoginAsync(string nomUtilisateur, string motDePasse)
        {
            using (var connection = CreateConnection())
            {
                var sql = @"
                SELECT COUNT(1) FROM Utilisateurs 
                WHERE NomUtilisateur = @NomUtilisateur 
                AND MotDePasse = @MotDePasse 
                AND EstActif = 1 
                AND EstSupprime = 0";

                var count = await connection.QuerySingleAsync<int>(sql, new { NomUtilisateur = nomUtilisateur, MotDePasse = motDePasse });
                return count > 0;
            }
        }

        public async Task<bool> UpdateDerniereConnexionAsync(int utilisateurId)
        {
            using (var connection = CreateConnection())
            {
                var sql = @"
                UPDATE Utilisateurs SET 
                    DerniereConnexion = @DerniereConnexion,
                    EstConnecte = 1
                WHERE Id = @Id";

                var result = await connection.ExecuteAsync(sql, new { Id = utilisateurId, DerniereConnexion = DateTime.Now });
                return result > 0;
            }
        }

        public async Task<bool> DeconnecterUtilisateurAsync(int utilisateurId)
        {
            using (var connection = CreateConnection())
            {
                var sql = "UPDATE Utilisateurs SET EstConnecte = 0 WHERE Id = @Id";
                var result = await connection.ExecuteAsync(sql, new { Id = utilisateurId });
                return result > 0;
            }
        }
    }
}

using Dapper;
using Super_Market.Core.Enums;
using Super_Market.Core.Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Super_Market.Data.Repositories
{
    public class VenteRepository : BaseRepository<Vente>
    {
        public VenteRepository() : base("Ventes")
        {
        }

        public override async Task<int> AddAsync(Vente entity)
        {
            using (var connection = CreateConnection())
            {
                var sql = @"
                INSERT INTO Ventes 
                (NumeroFacture, DateVente, ClientId, UtilisateurId, SousTotal, Remise, 
                 MontantTVA, Total, MontantPaye, MontantRendu, ModePaiement, Statut, 
                 Notes, EstFacturee, DateFacturation, DateCreation, UtilisateurCreation, EstActif)
                VALUES 
                (@NumeroFacture, @DateVente, @ClientId, @UtilisateurId, @SousTotal, @Remise,
                 @MontantTVA, @Total, @MontantPaye, @MontantRendu, @ModePaiement, @Statut,
                 @Notes, @EstFacturee, @DateFacturation, @DateCreation, @UtilisateurCreation, @EstActif);
                SELECT LAST_INSERT_ID();";

                return await connection.QuerySingleAsync<int>(sql, entity);
            }
        }

        public override async Task<bool> UpdateAsync(Vente entity)
        {
            using (var connection = CreateConnection())
            {
                var sql = @"
                UPDATE Ventes SET 
                    NumeroFacture = @NumeroFacture,
                    DateVente = @DateVente,
                    ClientId = @ClientId,
                    UtilisateurId = @UtilisateurId,
                    SousTotal = @SousTotal,
                    Remise = @Remise,
                    MontantTVA = @MontantTVA,
                    Total = @Total,
                    MontantPaye = @MontantPaye,
                    MontantRendu = @MontantRendu,
                    ModePaiement = @ModePaiement,
                    Statut = @Statut,
                    Notes = @Notes,
                    EstFacturee = @EstFacturee,
                    DateFacturation = @DateFacturation,
                    DateModification = @DateModification,
                    UtilisateurModification = @UtilisateurModification,
                    EstActif = @EstActif
                WHERE Id = @Id";

                var result = await connection.ExecuteAsync(sql, entity);
                return result > 0;
            }
        }

        public override async Task<IEnumerable<Vente>> SearchAsync(string searchTerm)
        {
            using (var connection = CreateConnection())
            {
                var sql = @"
                SELECT v.*, c.Nom as ClientNom, c.Prenom as ClientPrenom, u.Nom as UtilisateurNom
                FROM Ventes v
                LEFT JOIN Clients c ON v.ClientId = c.Id
                LEFT JOIN Utilisateurs u ON v.UtilisateurId = u.Id
                WHERE v.EstSupprime = 0 
                AND (v.NumeroFacture LIKE @SearchTerm 
                     OR c.Nom LIKE @SearchTerm 
                     OR c.Prenom LIKE @SearchTerm)
                ORDER BY v.DateVente DESC";

                return await connection.QueryAsync<Vente>(sql, new { SearchTerm = $"%{searchTerm}%" });
            }
        }

        public async Task<Vente> GetByNumeroFactureAsync(string numeroFacture)
        {
            using (var connection = CreateConnection())
            {
                var sql = "SELECT * FROM Ventes WHERE NumeroFacture = @NumeroFacture AND EstSupprime = 0";
                return await connection.QueryFirstOrDefaultAsync<Vente>(sql, new { NumeroFacture = numeroFacture });
            }
        }

        public async Task<IEnumerable<Vente>> GetVentesByDateAsync(DateTime date)
        {
            using (var connection = CreateConnection())
            {
                var sql = @"
                SELECT * FROM Ventes 
                WHERE DATE(DateVente) = DATE(@Date) 
                AND EstSupprime = 0 
                ORDER BY DateVente DESC";
                return await connection.QueryAsync<Vente>(sql, new { Date = date });
            }
        }

        public async Task<IEnumerable<Vente>> GetVentesByPeriodeAsync(DateTime dateDebut, DateTime dateFin)
        {
            using (var connection = CreateConnection())
            {
                var sql = @"
                SELECT * FROM Ventes 
                WHERE DateVente >= @DateDebut 
                AND DateVente <= @DateFin 
                AND EstSupprime = 0 
                ORDER BY DateVente DESC";
                return await connection.QueryAsync<Vente>(sql, new { DateDebut = dateDebut, DateFin = dateFin });
            }
        }

        public async Task<IEnumerable<Vente>> GetVentesByClientAsync(int clientId)
        {
            using (var connection = CreateConnection())
            {
                var sql = @"
                SELECT * FROM Ventes 
                WHERE ClientId = @ClientId 
                AND EstSupprime = 0 
                ORDER BY DateVente DESC";
                return await connection.QueryAsync<Vente>(sql, new { ClientId = clientId });
            }
        }

        public async Task<IEnumerable<Vente>> GetVentesByUtilisateurAsync(int utilisateurId)
        {
            using (var connection = CreateConnection())
            {
                var sql = @"
                SELECT * FROM Ventes 
                WHERE UtilisateurId = @UtilisateurId 
                AND EstSupprime = 0 
                ORDER BY DateVente DESC";
                return await connection.QueryAsync<Vente>(sql, new { UtilisateurId = utilisateurId });
            }
        }

        public async Task<decimal> GetChiffreAffairesAsync(DateTime? dateDebut = null, DateTime? dateFin = null)
        {
            using (var connection = CreateConnection())
            {
                var sql = @"
                SELECT COALESCE(SUM(Total), 0) 
                FROM Ventes 
                WHERE Statut = @StatutValidee 
                AND EstSupprime = 0";

                if (dateDebut.HasValue)
                    sql += " AND DateVente >= @DateDebut";
                if (dateFin.HasValue)
                    sql += " AND DateVente <= @DateFin";

                return await connection.QuerySingleAsync<decimal>(sql, new
                {
                    StatutValidee = StatutVente.Validee,
                    DateDebut = dateDebut,
                    DateFin = dateFin
                });
            }
        }

        public async Task<int> GetNombreVentesAsync(DateTime? dateDebut = null, DateTime? dateFin = null)
        {
            using (var connection = CreateConnection())
            {
                var sql = @"
                SELECT COUNT(*) 
                FROM Ventes 
                WHERE Statut = @StatutValidee 
                AND EstSupprime = 0";

                if (dateDebut.HasValue)
                    sql += " AND DateVente >= @DateDebut";
                if (dateFin.HasValue)
                    sql += " AND DateVente <= @DateFin";

                return await connection.QuerySingleAsync<int>(sql, new
                {
                    StatutValidee = StatutVente.Validee,
                    DateDebut = dateDebut,
                    DateFin = dateFin
                });
            }
        }

        public async Task<string> GenererNumeroFactureAsync()
        {
            using (var connection = CreateConnection())
            {
                var sql = @"
                SELECT COALESCE(MAX(CAST(SUBSTRING(NumeroFacture, 5) AS UNSIGNED)), 0) + 1 
                FROM Ventes 
                WHERE NumeroFacture LIKE 'FAC-%'";

                var numero = await connection.QuerySingleAsync<int>(sql);
                return $"FAC-{numero:D6}";
            }
        }

        public async Task<bool> AnnulerVenteAsync(int venteId, string motif)
        {
            using (var connection = CreateConnection())
            {
                var sql = @"
                UPDATE Ventes SET 
                    Statut = @StatutAnnulee,
                    Notes = CONCAT(COALESCE(Notes, ''), ' - Annulée: ', @Motif),
                    DateModification = @DateModification
                WHERE Id = @Id";

                var result = await connection.ExecuteAsync(sql, new
                {
                    Id = venteId,
                    StatutAnnulee = StatutVente.Annulee,
                    Motif = motif,
                    DateModification = DateTime.Now
                });
                return result > 0;
            }
        }

        public async Task<IEnumerable<dynamic>> GetStatistiquesVentesAsync(DateTime dateDebut, DateTime dateFin)
        {
            using (var connection = CreateConnection())
            {
                var sql = @"
                SELECT 
                    DATE(DateVente) as Date,
                    COUNT(*) as NombreVentes,
                    SUM(Total) as ChiffreAffaires,
                    AVG(Total) as PanierMoyen
                FROM Ventes 
                WHERE DateVente >= @DateDebut 
                AND DateVente <= @DateFin 
                AND Statut = @StatutValidee
                AND EstSupprime = 0
                GROUP BY DATE(DateVente)
                ORDER BY Date";

                return await connection.QueryAsync(sql, new
                {
                    DateDebut = dateDebut,
                    DateFin = dateFin,
                    StatutValidee = StatutVente.Validee
                });
            }
        }
    }
}

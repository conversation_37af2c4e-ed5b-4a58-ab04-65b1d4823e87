﻿using Krypton.Navigator;
using Krypton.Toolkit;
using Super_Market.POS.Properties;
using System;
using System.Drawing;
using System.Windows.Forms;

namespace Super_Market.POS
{
    public partial class FRM_LOGIN : KryptonForm
    {
        public FRM_LOGIN()
        {
            InitializeComponent();
        }
        private void KeypadButton_Click(object sender, EventArgs e)
        {
            KryptonButton btn = sender as KryptonButton;
            if (btn != null)
            {
                txtDisplay.Text += btn.Text;
            }
        }
        private void buttonLeft_Click(object sender, System.EventArgs e)
        {
            bool isHeaderBarCheckButtonGroup = kryptonNavigator1.NavigatorMode == NavigatorMode.HeaderBarCheckButtonGroup;
            kryptonNavigator1.NavigatorMode = (isHeaderBarCheckButtonGroup ? NavigatorMode.HeaderBarCheckButtonOnly : NavigatorMode.HeaderBarCheckButtonGroup);
            buttonLeft.TypeRestricted = ((!isHeaderBarCheckButtonGroup) ? PaletteNavButtonSpecStyle.ArrowLeft : PaletteNavButtonSpecStyle.ArrowRight);
            base.Size = (isHeaderBarCheckButtonGroup ? new Size(280, 459) : new Size(507, 459));

        }

        private void btnClear_Click(object sender, EventArgs e)
        {
            txtDisplay.Text = "";
        }

        private void btnBack_Click(object sender, EventArgs e)
        {
            if (txtDisplay.Text.Length > 0)
                txtDisplay.Text = txtDisplay.Text.Substring(0, txtDisplay.Text.Length - 1);
        }

        private void btnEnter_Click(object sender, EventArgs e)
        {
            MessageBox.Show("Entered: " + txtDisplay.Text);
            this.Hide();
            new FRM_POS().ShowDialog();
            this.Close();
        }

        private void btnShow_Click(object sender, EventArgs e)
        {
            txtDisplay.PasswordChar = ((!(txtDisplay.Tag as string == "0")) ? '#' : '\0');
            txtDisplay.ButtonSpecs[0].Image = ((txtDisplay.Tag as string == "0") ? Properties.Resources.hide : Resources.eye);
            txtDisplay.Tag = ((txtDisplay.Tag as string == "0") ? "1" : "0");
        }

        private void btnExit_Click(object sender, EventArgs e)
        {
            Application.Exit();
        }
    }
}

﻿using Krypton.Navigator;
using Krypton.Toolkit;
using Super_Market.POS.Properties;
using Super_Market.Business.Services;
using Super_Market.Core.Models;
using System;
using System.Drawing;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Super_Market.POS
{
    public partial class FRM_LOGIN : KryptonForm
    {
        private readonly UtilisateurService _utilisateurService;

        public FRM_LOGIN()
        {
            InitializeComponent();
            _utilisateurService = new UtilisateurService();
            InitialiserFormulairePOS();
        }

        private void InitialiserFormulairePOS()
        {
            this.Text = "ZinStore POS - Connexion";
            this.WindowState = FormWindowState.Maximized;

            // Configuration spécifique POS
            txtDisplay.Text = "";
            txtDisplay.Tag = "0"; // Pour le mode mot de passe
        }

        private void KeypadButton_Click(object sender, EventArgs e)
        {
            KryptonButton btn = sender as KryptonButton;
            if (btn != null)
            {
                txtDisplay.Text += btn.Text;
            }
        }

        private void buttonLeft_Click(object sender, System.EventArgs e)
        {
            bool isHeaderBarCheckButtonGroup = kryptonNavigator1.NavigatorMode == NavigatorMode.HeaderBarCheckButtonGroup;
            kryptonNavigator1.NavigatorMode = (isHeaderBarCheckButtonGroup ? NavigatorMode.HeaderBarCheckButtonOnly : NavigatorMode.HeaderBarCheckButtonGroup);
            buttonLeft.TypeRestricted = ((!isHeaderBarCheckButtonGroup) ? PaletteNavButtonSpecStyle.ArrowLeft : PaletteNavButtonSpecStyle.ArrowRight);
            base.Size = (isHeaderBarCheckButtonGroup ? new Size(280, 459) : new Size(507, 459));
        }

        private void btnClear_Click(object sender, EventArgs e)
        {
            txtDisplay.Text = "";
        }

        private void btnBack_Click(object sender, EventArgs e)
        {
            if (txtDisplay.Text.Length > 0)
                txtDisplay.Text = txtDisplay.Text.Substring(0, txtDisplay.Text.Length - 1);
        }

        private async void btnEnter_Click(object sender, EventArgs e)
        {
            await TenterConnexionPOS();
        }

        private async Task TenterConnexionPOS()
        {
            try
            {
                if (string.IsNullOrWhiteSpace(txtDisplay.Text))
                {
                    MessageBox.Show("Veuillez saisir votre code d'accès.",
                                  "Code requis",
                                  MessageBoxButtons.OK,
                                  MessageBoxIcon.Warning);
                    return;
                }

                // Pour le POS, on utilise le code comme nom d'utilisateur et mot de passe
                var utilisateur = await _utilisateurService.AuthentifierAsync(txtDisplay.Text, txtDisplay.Text);

                if (utilisateur != null)
                {
                    // Vérifier que l'utilisateur peut utiliser le POS
                    if (utilisateur.TypeUtilisateur == Core.Enums.TypeUtilisateur.Caissier ||
                        utilisateur.TypeUtilisateur == Core.Enums.TypeUtilisateur.Vendeur ||
                        utilisateur.TypeUtilisateur == Core.Enums.TypeUtilisateur.Gerant ||
                        utilisateur.TypeUtilisateur == Core.Enums.TypeUtilisateur.Administrateur)
                    {
                        // Connexion réussie
                        this.Hide();
                        var formPOS = new FRM_POS(utilisateur);
                        formPOS.ShowDialog();
                        this.Close();
                    }
                    else
                    {
                        MessageBox.Show("Vous n'êtes pas autorisé à utiliser le point de vente.",
                                      "Accès refusé",
                                      MessageBoxButtons.OK,
                                      MessageBoxIcon.Error);
                        txtDisplay.Text = "";
                    }
                }
                else
                {
                    MessageBox.Show("Code d'accès incorrect.",
                                  "Erreur de connexion",
                                  MessageBoxButtons.OK,
                                  MessageBoxIcon.Error);
                    txtDisplay.Text = "";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors de la connexion: {ex.Message}",
                              "Erreur",
                              MessageBoxButtons.OK,
                              MessageBoxIcon.Error);
                txtDisplay.Text = "";
            }
        }

        private void btnShow_Click(object sender, EventArgs e)
        {
            txtDisplay.PasswordChar = ((!(txtDisplay.Tag as string == "0")) ? '#' : '\0');
            txtDisplay.ButtonSpecs[0].Image = ((txtDisplay.Tag as string == "0") ? Properties.Resources.hide : Resources.eye);
            txtDisplay.Tag = ((txtDisplay.Tag as string == "0") ? "1" : "0");
        }

        private void btnExit_Click(object sender, EventArgs e)
        {
            var result = MessageBox.Show("Êtes-vous sûr de vouloir quitter le point de vente ?",
                                       "Confirmation",
                                       MessageBoxButtons.YesNo,
                                       MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                Application.Exit();
            }
        }

        private void FRM_LOGIN_Load(object sender, EventArgs e)
        {
            // Vérifier la connexion à la base de données
            try
            {
                bool connectionOk = Super_Market.Data.DatabaseConfig.TestConnection();
                if (!connectionOk)
                {
                    MessageBox.Show("Impossible de se connecter à la base de données.\n" +
                                  "Veuillez contacter l'administrateur système.",
                                  "Erreur de base de données",
                                  MessageBoxButtons.OK,
                                  MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors de la vérification de la base de données: {ex.Message}",
                              "Erreur",
                              MessageBoxButtons.OK,
                              MessageBoxIcon.Error);
            }
        }
    }
}

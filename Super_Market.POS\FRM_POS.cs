﻿using Krypton.Toolkit;
using Super_Market.Core.Models;
using Super_Market.Business.Services;
using System;
using System.Collections.Generic;
using System.Windows.Forms;
using System.Linq;

namespace Super_Market.POS
{
    public partial class FRM_POS : KryptonForm
    {
        private readonly Utilisateur _utilisateurConnecte;
        private readonly VenteService _venteService;
        private readonly ProduitService _produitService;
        private readonly ClientService _clientService;
        private readonly List<VenteDetail> _panierActuel;
        private Client _clientActuel;
        private decimal _totalVente = 0;

        public FRM_POS(Utilisateur utilisateur)
        {
            InitializeComponent();
            _utilisateurConnecte = utilisateur;
            _venteService = new VenteService();
            _produitService = new ProduitService();
            _clientService = new ClientService();
            _panierActuel = new List<VenteDetail>();

            InitialiserFormulairePOS();
        }

        public FRM_POS()
        {
            InitializeComponent();
            // Constructeur par défaut pour le designer
        }

        private void InitialiserFormulairePOS()
        {
            this.Text = $"ZinStore POS - {_utilisateurConnecte.Prenom} {_utilisateurConnecte.Nom}";
            this.WindowState = FormWindowState.Maximized;

            // Initialiser l'interface
            lblUtilisateur.Text = $"Caissier: {_utilisateurConnecte.Prenom} {_utilisateurConnecte.Nom}";
            lblDateHeure.Text = DateTime.Now.ToString("dd/MM/yyyy HH:mm");
            lblTotal.Text = "0,00 DZD";

            // Configurer la grille du panier
            ConfigurerGrillePanier();

            // Démarrer le timer pour l'heure
            timerHeure.Start();

            // Focus sur le champ code-barres
            txtCodeBarre.Focus();
        }

        private void ConfigurerGrillePanier()
        {
            dgvPanier.AutoGenerateColumns = false;
            dgvPanier.Columns.Clear();

            dgvPanier.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Produit",
                HeaderText = "Produit",
                DataPropertyName = "NomProduit",
                Width = 200
            });

            dgvPanier.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Prix",
                HeaderText = "Prix Unit.",
                DataPropertyName = "PrixUnitaire",
                Width = 80,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "F2" }
            });

            dgvPanier.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Quantite",
                HeaderText = "Qté",
                DataPropertyName = "Quantite",
                Width = 60,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "F2" }
            });

            dgvPanier.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "SousTotal",
                HeaderText = "Sous-Total",
                DataPropertyName = "SousTotal",
                Width = 100,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "F2" }
            });
        }

        private async void txtCodeBarre_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Enter)
            {
                await AjouterProduitAuPanier(txtCodeBarre.Text.Trim());
                txtCodeBarre.Text = "";
                e.Handled = true;
            }
        }

        private async System.Threading.Tasks.Task AjouterProduitAuPanier(string codeBarre)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(codeBarre))
                    return;

                var produit = await _produitService.GetProduitByCodeBarreAsync(codeBarre);
                if (produit == null)
                {
                    MessageBox.Show("Produit introuvable.", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // Vérifier si le produit est déjà dans le panier
                var ligneExistante = _panierActuel.FirstOrDefault(p => p.ProduitId == produit.Id);
                if (ligneExistante != null)
                {
                    ligneExistante.Quantite += 1;
                    ligneExistante.SousTotal = ligneExistante.Quantite * ligneExistante.PrixUnitaire;
                }
                else
                {
                    var nouvelleLigne = new VenteDetail
                    {
                        ProduitId = produit.Id,
                        Produit = produit,
                        Quantite = 1,
                        PrixUnitaire = _produitService.CalculerPrixVenteActuel(produit),
                        TauxTVA = produit.TVA ?? 19,
                        SousTotal = _produitService.CalculerPrixVenteActuel(produit)
                    };

                    _panierActuel.Add(nouvelleLigne);
                }

                ActualiserAffichagePanier();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors de l'ajout du produit: {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ActualiserAffichagePanier()
        {
            // Créer une liste d'objets anonymes pour l'affichage
            var panierAffichage = _panierActuel.Select(p => new
            {
                NomProduit = p.Produit?.Nom ?? "Produit inconnu",
                PrixUnitaire = p.PrixUnitaire,
                Quantite = p.Quantite,
                SousTotal = p.SousTotal
            }).ToList();

            dgvPanier.DataSource = panierAffichage;

            // Calculer le total
            _totalVente = _panierActuel.Sum(p => p.SousTotal);
            lblTotal.Text = $"{_totalVente:F2} DZD";
            lblNombreArticles.Text = $"{_panierActuel.Sum(p => p.Quantite):F0} article(s)";
        }

        private void btnVider_Click(object sender, EventArgs e)
        {
            if (_panierActuel.Count > 0)
            {
                var result = MessageBox.Show("Êtes-vous sûr de vouloir vider le panier ?",
                                           "Confirmation",
                                           MessageBoxButtons.YesNo,
                                           MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    _panierActuel.Clear();
                    _clientActuel = null;
                    ActualiserAffichagePanier();
                    lblClient.Text = "Client: Aucun";
                }
            }
        }

        private async void btnValider_Click(object sender, EventArgs e)
        {
            await ValiderVente();
        }

        private async System.Threading.Tasks.Task ValiderVente()
        {
            try
            {
                if (_panierActuel.Count == 0)
                {
                    MessageBox.Show("Le panier est vide.", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // Créer la vente
                var vente = new Vente
                {
                    DateVente = DateTime.Now,
                    ClientId = _clientActuel?.Id,
                    UtilisateurId = _utilisateurConnecte.Id,
                    ModePaiement = Core.Enums.ModePaiement.Especes, // Par défaut
                    Statut = Core.Enums.StatutVente.EnCours,
                    DateCreation = DateTime.Now,
                    UtilisateurCreation = _utilisateurConnecte.Id.ToString(),
                    EstActif = true
                };

                var venteId = await _venteService.CreerVenteAsync(vente, _panierActuel);
                await _venteService.ValiderVenteAsync(venteId);

                MessageBox.Show($"Vente validée avec succès!\nNuméro de facture: {vente.NumeroFacture}",
                              "Succès",
                              MessageBoxButtons.OK,
                              MessageBoxIcon.Information);

                // Réinitialiser le panier
                _panierActuel.Clear();
                _clientActuel = null;
                ActualiserAffichagePanier();
                lblClient.Text = "Client: Aucun";
                txtCodeBarre.Focus();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors de la validation: {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnDeconnexion_Click(object sender, EventArgs e)
        {
            var result = MessageBox.Show("Êtes-vous sûr de vouloir vous déconnecter ?",
                                       "Confirmation",
                                       MessageBoxButtons.YesNo,
                                       MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                this.Hide();
                var loginForm = new FRM_LOGIN();
                loginForm.ShowDialog();
                this.Close();
            }
        }

        private void timerHeure_Tick(object sender, EventArgs e)
        {
            lblDateHeure.Text = DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss");
        }

        private void FRM_POS_Load(object sender, EventArgs e)
        {
            txtCodeBarre.Focus();
        }

        private void FRM_POS_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (_panierActuel.Count > 0)
            {
                var result = MessageBox.Show("Il y a des articles dans le panier. Êtes-vous sûr de vouloir fermer ?",
                                           "Confirmation",
                                           MessageBoxButtons.YesNo,
                                           MessageBoxIcon.Question);

                if (result == DialogResult.No)
                {
                    e.Cancel = true;
                }
            }
        }
    }
}

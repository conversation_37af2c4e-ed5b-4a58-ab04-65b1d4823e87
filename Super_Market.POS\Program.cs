﻿using System;
using System.Windows.Forms;

namespace Super_Market.POS
{
    internal static class Program
    {
        /// <summary>
        /// Point d'entrée principal de l'application ZinStore POS - Point de Vente
        /// </summary>
        [STAThread]
        static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);

            // Configurer la chaîne de connexion à la base de données
            Super_Market.Data.DatabaseConfig.ConnectionString =
                "Server=localhost;Database=zinstore_db;Uid=root;Pwd=;CharSet=utf8;";

            // Démarrer avec le formulaire de connexion POS
            Application.Run(new FRM_LOGIN());
        }
    }
}

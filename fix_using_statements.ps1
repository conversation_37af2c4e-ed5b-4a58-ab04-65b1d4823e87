# Script PowerShell pour corriger les déclarations using pour C# 7.3
# Ce script remplace "using var" par "using (var" et ajoute les accolades fermantes

$repositoryPath = "Super_Market.Data\Repositories"
$files = Get-ChildItem -Path $repositoryPath -Filter "*.cs" -Recurse

foreach ($file in $files) {
    Write-Host "Traitement de $($file.Name)..."
    
    $content = Get-Content $file.FullName -Raw
    $originalContent = $content
    
    # Remplacer les patterns using var
    $content = $content -replace 'using var connection = CreateConnection\(\);', 'using (var connection = CreateConnection())`n            {'
    
    # Ajouter les accolades fermantes avant les return statements
    $content = $content -replace '(\s+)return await connection\.([^;]+);(\s*)\}', '$1    return await connection.$2;$3    }`3}'
    
    # Ajouter les accolades fermantes avant les autres statements de fin
    $content = $content -replace '(\s+)(var \w+ = await connection\.[^;]+;)(\s+)(return \w+[^;]*;)(\s*)\}', '$1    $2$3    $4$5    }`5}'
    
    if ($content -ne $originalContent) {
        Set-Content -Path $file.FullName -Value $content -Encoding UTF8
        Write-Host "  -> Modifié"
    } else {
        Write-Host "  -> Aucune modification nécessaire"
    }
}

Write-Host "Correction terminée!"
